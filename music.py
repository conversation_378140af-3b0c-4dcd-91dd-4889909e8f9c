import os
import re
import asyncio
import subprocess
import zipfile
import sqlite3
from collections import defaultdict
from datetime import datetime, timedelta
import pytz  # Add timezone support
import chardet  # For encoding detection
import unicodedata  # For unicode normalization
import locale
import sys
from pathlib import Path  # Better path handling

import requests
import m3u8
from sanitize_filename import sanitize as sanitize_filename

# Set UTF-8 encoding for Python
if sys.platform.startswith('linux') or sys.platform == 'darwin':
    try:
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except:
            pass

# Force UTF-8 encoding for file system
if hasattr(sys, 'setfilesystemencoding'):
    sys.setfilesystemencoding('utf-8')

from telethon import TelegramClient, events
from telethon.errors import RPCError
from telethon.tl.types import DocumentAttributeVideo, DocumentAttributeAudio
from telethon.tl.custom import Button
from collections import defaultdict

# Supabase - commented out as we're not using database
# from supabase import create_client, Client as SupabaseClient

# SUPABASE_URL = "https://wjanjmsywbydjbfrdkaz.supabase.co"
# SUPABASE_KEY = (
#     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
#     "eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0."
#     "ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE"
# )
# supabase: SupabaseClient = create_client(SUPABASE_URL, SUPABASE_KEY)

def fix_mojibake(text):
    """
    Try to fix mojibake (UTF-8 interpreted as Latin-1).
    """
    if not isinstance(text, str):
        return text
        
    try:
        # Check if text contains typical mojibake patterns
        if any(ord(c) in range(0x80, 0x100) for c in text):
            # Log the mojibake detection
            print(f"[{get_beijing_time()}][Mojibake] Detected mojibake: {repr(text)}")
            
            # Try to encode as latin-1 and decode as utf-8
            try:
                fixed = text.encode('latin-1', errors='ignore').decode('utf-8', errors='ignore')
                # Validate the result contains CJK characters
                if any('\u4e00' <= c <= '\u9fff' or  # Chinese
                       '\u3040' <= c <= '\u309f' or  # Hiragana
                       '\u30a0' <= c <= '\u30ff' or  # Katakana
                       '\uac00' <= c <= '\ud7af'     # Korean
                       for c in fixed):
                    print(f"[{get_beijing_time()}][Mojibake] Fixed to: {repr(fixed)}")
                    return fixed
            except Exception as e:
                print(f"[{get_beijing_time()}][Mojibake] Fix failed: {e}")
    except:
        pass
    return text

def ensure_utf8(text):
    """
    Ensure text is properly encoded in UTF-8.
    Detects encoding and converts if necessary.
    """
    if text is None:
        return ""
        
    if isinstance(text, bytes):
        # Try common encodings in order
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'shift_jis', 'euc-jp', 'euc-kr', 'latin-1', 'iso-8859-1']
        
        # First try chardet detection
        try:
            detected = chardet.detect(text)
            if detected['encoding'] and detected['confidence'] > 0.7:
                encodings.insert(0, detected['encoding'])
        except:
            pass
            
        # Try each encoding
        for encoding in encodings:
            try:
                decoded = text.decode(encoding)
                # Validate that it's proper text
                if decoded and not any(ord(c) > 0xFFFF for c in decoded):
                    text = decoded
                    break
            except (UnicodeDecodeError, AttributeError):
                continue
        else:
            # Last resort: decode with replacement
            text = text.decode('utf-8', errors='replace')
    
    # Ensure it's a string
    if not isinstance(text, str):
        text = str(text)
    
    # Don't try to fix mojibake - prevent creating corrupted filenames
    # text = fix_mojibake(text)  # REMOVED - this was causing issues
    
    # Normalize unicode to handle combining characters
    text = unicodedata.normalize('NFC', text)
    
    # Remove any null bytes
    text = text.replace('\x00', '')
    
    return text


def clean_filename_for_output(filename):
    """
    Clean filename for output files, removing duplicates and special characters.
    """
    # Don't use ensure_utf8 here - just work with the string as-is
    original = filename
    
    # Remove common patterns like "(1)", "(2)", etc.
    import re
    filename = re.sub(r'\s*\(\d+\)\s*', '', filename)
    
    # Remove duplicate speed indicators if any
    filename = re.sub(r'_\d+\.\d+x', '', filename)
    filename = re.sub(r'_speed_\d+\.\d+x', '', filename)
    filename = re.sub(r'^Speed\s+\d+\.\d+x\s*[–—-]\s*', '', filename)  # Remove "Speed X.Xx – " prefix at start
    
    # Remove trailing speed indicators
    filename = re.sub(r'\s*_*\d+\.\d+x\s*$', '', filename)
    
    # Remove trailing "_speed" if it exists
    filename = re.sub(r'_speed$', '', filename)
    
    # Clean up multiple spaces/underscores  
    filename = re.sub(r'\s+', ' ', filename)
    filename = re.sub(r'_+', '_', filename)
    filename = re.sub(r'\s*_\s*', '_', filename)  # Replace " _ " with "_"
    filename = filename.strip('_ ')
    
    if filename != original:
        print(f"[{get_beijing_time()}][Clean] '{original}' -> '{filename}'")
    
    return filename

def safe_path_join(*parts):
    """
    Safely join path parts ensuring UTF-8 encoding.
    """
    safe_parts = [ensure_utf8(part) for part in parts]
    return os.path.join(*safe_parts)

def safe_basename(path):
    """
    Get basename with proper encoding.
    """
    path = ensure_utf8(path)
    basename = os.path.basename(path)
    basename = ensure_utf8(basename)
    
    # If basename looks like a telegram temp file, try to extract original name
    # Telegram temp files often look like: audio_2024_01_01_123456.oga
    if basename.startswith(('audio_', 'voice_', 'video_')) and '_' in basename:
        # This is likely a temp file, don't use it
        print(f"[{get_beijing_time()}][Bot] Detected Telegram temp filename: {basename}")
        return None
    
    return basename

def safe_dirname(path):
    """
    Get dirname with proper encoding.
    """
    return ensure_utf8(os.path.dirname(ensure_utf8(path)))

def sanitize_filename_utf8(filename):
    """
    Sanitize filename ensuring proper UTF-8 encoding.
    """
    # Ensure UTF-8 encoding first
    filename = ensure_utf8(filename)
    # Then sanitize for filesystem
    return sanitize_filename(filename)


def get_beijing_time():
    """Return formatted Beijing time string"""
    beijing_tz = pytz.timezone('Asia/Shanghai')
    beijing_time = datetime.now(beijing_tz)
    return beijing_time.strftime('%Y-%m-%d %H:%M:%S %Z')


def get_file_size_gb(file_path):
    """Get file size in GB"""
    if os.path.exists(file_path):
        size_bytes = os.path.getsize(file_path)
        size_gb = size_bytes / (1024 * 1024 * 1024)
        return size_gb
    return 0

BOT_TOKEN = '7222442621:AAG0T-pl-hLEvzBfHtK3xvZT8S3f9jwoECA'
API_ID = 25432929
API_HASH = '965c5d22f0b9d1d0326e84bbb2bb18c1'

client = TelegramClient('apple_music_downloader_bot', API_ID, API_HASH).start(bot_token=BOT_TOKEN)

# Global downloader instance to reuse token
global_downloader = None

#########################
# Database Setup
#########################
DB_PATH = "/home/<USER>/working/apple_music_cache.db"

def init_database():
    """Initialize the database and create the am table if it doesn't exist."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS am (
            id TEXT PRIMARY KEY,
            country TEXT NOT NULL,
            kind TEXT NOT NULL,
            name TEXT,
            artist TEXT,
            release_date TEXT,
            last_modified TEXT,
            url TEXT,
            file_id TEXT NOT NULL,
            file_size INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(id, country, kind)
        )
    ''')
    
    # Create index for faster lookups
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_am_lookup 
        ON am (id, country, kind)
    ''')
    
    conn.commit()
    conn.close()
    print(f"[{get_beijing_time()}][DB] Database initialized")

def get_cached_file(id_, country, kind):
    """Check if we have a cached file_id for this Apple Music item."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT file_id, name, artist, release_date, last_modified, url
        FROM am
        WHERE id = ? AND country = ? AND kind = ?
    ''', (id_, country, kind))
    
    result = cursor.fetchone()
    conn.close()
    
    if result:
        return {
            'file_id': result[0],
            'name': result[1],
            'artist': result[2],
            'release_date': result[3],
            'last_modified': result[4],
            'url': result[5]
        }
    return None

def save_to_cache(info, file_id, file_size=None):
    """Save the file_id to cache for future use."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT OR REPLACE INTO am 
        (id, country, kind, name, artist, release_date, last_modified, url, file_id, file_size, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    ''', (
        info['id'],
        info['country'],
        info['kind'],
        info.get('name'),
        info.get('artist'),
        info.get('release_date'),
        info.get('last_modified'),
        info.get('url'),
        file_id,
        file_size
    ))
    
    conn.commit()
    conn.close()
    print(f"[{get_beijing_time()}][DB] Cached file_id for {info['id']} ({info['country']}, {info['kind']})")

# Initialize database when module loads
init_database()

# Global storage for audio concatenation sessions
audio_sessions = defaultdict(lambda: {"files": [], "waiting_for_next": False})


#########################
# 2) AppleMusicDownloader
#########################

class AppleMusicDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.token_expires_at = None
        self.update_token()

    def update_token(self):
        # Check if token is still valid (assume 1 hour validity)
        if self.token and self.token_expires_at and datetime.now(pytz.UTC) < self.token_expires_at:
            print(f"[{get_beijing_time()}][Downloader] Using cached token (valid until {self.token_expires_at})")
            return
            
        print(f"[{get_beijing_time()}][Downloader] Fetching new token from Apple Music page...")
        start_time = datetime.now()
        
        resp = self.session.get("https://music.apple.com/us/album/positions-deluxe-edition/1553944254")
        jspath = re.search(r'crossorigin src="(/assets/index.+?\.js)"', resp.text)
        if not jspath:
            raise ValueError("Cannot find JS path from Apple Music page.")
        jsres = self.session.get("https://music.apple.com" + jspath.group(1))
        token_match = re.search(r'(eyJhbGc.+?)"', jsres.text)
        if not token_match:
            raise ValueError("Cannot find token from Apple Music JS.")
        token = token_match.group(1)

        self.token = token
        self.token_expires_at = datetime.now(pytz.UTC) + timedelta(hours=1)
        
        self.session.headers.update({
            'authorization': f"Bearer {token}",
            'origin': 'https://music.apple.com'
        })
        
        elapsed = (datetime.now() - start_time).total_seconds()
        print(f"[{get_beijing_time()}][Downloader] Token updated successfully in {elapsed:.2f} seconds.")

    def get_json(self, id_, country, kind):
        """Call Apple Music API to get metadata."""
        print(f"[{get_beijing_time()}][Downloader] Requesting Apple Music API: id={id_}, country={country}, kind={kind}")
        params = {
            'extend': 'editorialVideo',
            'fields[artists]': 'artwork,editorialVideo,url,name',
            'fields[albums]': 'artwork,editorialVideo,url,name,artistName,releaseDate',
            'fields[playlists]': 'artwork,editorialVideo,url,name,curatorName,lastModifiedDate,description,trackTypes'
        }
        url = f"https://amp-api.music.apple.com/v1/catalog/{country}/{kind}s/{id_}"
        resp = self.session.get(url, params=params)
        if resp.status_code == 401:
            print(f"[{get_beijing_time()}][Downloader] Token might have expired, retrying...")
            self.update_token()
            resp = self.session.get(url, params=params)
        resp.raise_for_status()
        print(f"[{get_beijing_time()}][Downloader] API responded successfully.")
        return resp.json()

    def fix_artwork_url(self, url: str):
        """
        Replace {w}/{h} with 6000, and convert bb.jpg -> bb.png
        to get a high-resolution artwork.
        """
        url = url.replace("{w}", "6000").replace("{h}", "6000")
        if "bb.jpg" in url:
            url = url.replace("bb.jpg", "bb.png")
        return url

    def download_static_artwork(self, url: str, output_path: str):
        """Download static cover."""
        print(f"[{get_beijing_time()}][Downloader] Downloading static artwork from: {url}")
        start_time = datetime.now()
        
        try:
            r = self.session.get(url, timeout=30)
            if r.status_code != 200:
                print(f"[{get_beijing_time()}][Downloader] Failed to download artwork. HTTP status: {r.status_code}")
                return None
            
            file_size = len(r.content) / (1024 * 1024)  # MB
            with open(output_path, "wb") as f:
                f.write(r.content)
            
            elapsed = (datetime.now() - start_time).total_seconds()
            print(f"[{get_beijing_time()}][Downloader] Artwork saved to {output_path} ({file_size:.2f} MB in {elapsed:.2f} seconds)")
            return output_path
        except requests.exceptions.Timeout:
            print(f"[{get_beijing_time()}][Downloader] Timeout downloading artwork after 30 seconds")
            return None
        except Exception as e:
            print(f"[{get_beijing_time()}][Downloader] Error downloading artwork: {e}")
            return None

    def download_video(self, m3u8_url: str, output_path: str):
        """Download dynamic cover (m3u8)."""
        print(f"[{get_beijing_time()}][Downloader] Downloading video (m3u8) from: {m3u8_url}")
        start_time = datetime.now()
        
        try:
            # Parse master playlist to get best quality stream
            playlist = m3u8.load(m3u8_url)
            
            best_stream_url = None
            best_resolution = None
            
            if playlist.is_variant and playlist.playlists:
                # Find best quality stream
                streams = []
                for p in playlist.playlists:
                    stream_info = p.stream_info
                    if stream_info and hasattr(stream_info, 'resolution') and stream_info.resolution:
                        streams.append({
                            'uri': p.absolute_uri,
                            'resolution': stream_info.resolution,
                            'bandwidth': int(stream_info.bandwidth) if hasattr(stream_info, 'bandwidth') else 0
                        })
                
                if streams:
                    # Sort to get best quality
                    streams = sorted(
                        streams,
                        key=lambda x: (x['resolution'], x['bandwidth']),
                        reverse=True
                    )
                    best_stream_url = streams[0]['uri']
                    best_resolution = streams[0]['resolution']
                    print(f"[{get_beijing_time()}][Downloader] Selected best stream: {best_resolution[0]}x{best_resolution[1]}")
                else:
                    best_stream_url = playlist.playlists[0].absolute_uri
                    print(f"[{get_beijing_time()}][Downloader] Using first available stream")
            else:
                best_stream_url = m3u8_url
                print(f"[{get_beijing_time()}][Downloader] Using direct m3u8 URL")
            
            # Check if the stream uses byte ranges (Apple's special HLS)
            stream_playlist = m3u8.load(best_stream_url)
            uses_byterange = False
            mp4_url = None
            
            if stream_playlist.segments:
                uses_byterange = any(seg.byterange for seg in stream_playlist.segments)
                if uses_byterange and stream_playlist.segments:
                    # All segments point to the same MP4 file with byte ranges
                    # Download the complete MP4 file directly
                    mp4_url = stream_playlist.segments[0].absolute_uri
                    print(f"[{get_beijing_time()}][Downloader] Stream uses byte ranges, downloading complete MP4 directly")
            
            if mp4_url:
                # Download the complete MP4 file directly (this gets the full video)
                import requests
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                    'Accept': '*/*',
                    'Origin': 'https://music.apple.com',
                    'Referer': 'https://music.apple.com/',
                }
                
                # Download directly to output path - no modifications
                response = requests.get(mp4_url, headers=headers, stream=True)
                if response.status_code == 200:
                    with open(output_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    
                    file_size = os.path.getsize(output_path) / (1024 * 1024)
                    elapsed = (datetime.now() - start_time).total_seconds()
                    
                    # Check video properties for logging only
                    probe_cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration,start_time', '-of', 'csv=p=0', output_path]
                    duration_result = subprocess.run(probe_cmd, capture_output=True, text=True)
                    if duration_result.stdout:
                        parts = duration_result.stdout.strip().split(',')
                        actual_duration = float(parts[0]) if parts else 0
                        video_start_time = float(parts[1]) if len(parts) > 1 else 0
                        if video_start_time > 0.1:
                            print(f"[{get_beijing_time()}][Downloader] Video saved to {output_path} ({file_size:.2f} MB, duration={actual_duration:.1f}s) - Note: playback starts at {video_start_time:.1f}s")
                        else:
                            print(f"[{get_beijing_time()}][Downloader] Video saved to {output_path} ({file_size:.2f} MB, duration={actual_duration:.1f}s)")
                    else:
                        print(f"[{get_beijing_time()}][Downloader] Video saved to {output_path} ({file_size:.2f} MB in {elapsed:.2f}s)")
                    
                    return output_path
                else:
                    print(f"[{get_beijing_time()}][Downloader] Failed to download MP4 directly, falling back to ffmpeg")
            
            # Fallback to ffmpeg for standard HLS streams
            result = subprocess.run([
                'ffmpeg', '-loglevel', 'quiet',
                '-protocol_whitelist', 'file,http,https,tcp,tls',
                '-y', '-i', best_stream_url,
                '-c', 'copy',
                '-bsf:a', 'aac_adtstoasc',
                '-movflags', '+faststart',
                output_path
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                # Fallback to simpler command
                subprocess.run([
                    'ffmpeg', '-loglevel', 'quiet',
                    '-y', '-i', best_stream_url,
                    '-c', 'copy', output_path
                ], check=True)
            
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            elapsed = (datetime.now() - start_time).total_seconds()
            
            # Check actual duration
            probe_cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'csv=p=0', output_path]
            duration_result = subprocess.run(probe_cmd, capture_output=True, text=True)
            actual_duration = float(duration_result.stdout.strip()) if duration_result.stdout else 0
            
            print(f"[{get_beijing_time()}][Downloader] Video saved to {output_path} ({file_size:.2f} MB in {elapsed:.2f}s, {actual_duration:.1f}s duration)")
            return output_path
            
        except Exception as e:
            print(f"[{get_beijing_time()}][Downloader] Error downloading m3u8: {e}")
            return None


#########################
# 2.1) Utility: Get media resolution
#########################

def get_media_resolution(file_path: str) -> str:
    """
    Use ffprobe to get file resolution (width x height).
    Returns an empty string on failure.
    """
    try:
        cmd = [
            'ffprobe', '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height',
            '-of', 'csv=s=x:p=0', file_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        resolution = result.stdout.strip()  # e.g. "1920x1080"
        return resolution
    except Exception as e:
        print(f"[{get_beijing_time()}][Resolution] ffprobe failed for {file_path}: {e}")
        return ""


#########################
# 3) Extended Downloader
#########################

class AppleMusicDownloaderExtended(AppleMusicDownloader):
    def get_m3u8_urls(self, data_json):
        """
        Retrieve dynamic cover links from editorialVideo with fallback logic,
        so we can handle if some keys are missing.
        """
        base = data_json['data'][0]['attributes'].get('editorialVideo')
        if not base:
            return []

        # 'data'[0]['type'] will be one of: 'albums', 'playlists', 'artists'
        kind = data_json['data'][0]['type']

        # We'll collect possible tall and square links with fallback
        # then return them if they exist.
        # Because Apple naming differs for artists vs albums/playlists.
        tall_url = None
        square_url = None

        if kind == 'artists':
            # For artists, code from 'FetchAll' tries motionArtistFullscreen16x9, then wide16x9, ...
            try:
                tall_url = base['motionArtistFullscreen16x9']['video']
            except KeyError:
                try:
                    tall_url = base['motionArtistWide16x9']['video']
                except KeyError:
                    tall_url = None

            # square
            try:
                square_url = base['motionArtistSquare1x1']['video']
            except KeyError:
                square_url = None

        else:
            # For albums/playlists
            # 'FetchAll' uses motionDetailTall for 'tall', and if KeyError we just skip
            try:
                tall_url = base['motionDetailTall']['video']
            except KeyError:
                tall_url = None

            # square
            # might be motionDetailSquare or fallback to motionSquareVideo1x1
            try:
                square_url = base['motionDetailSquare']['video']
            except KeyError:
                try:
                    square_url = base['motionSquareVideo1x1']['video']
                except KeyError:
                    square_url = None

        results = []
        if tall_url:
            results.append(("tall", tall_url))
        if square_url:
            results.append(("square", square_url))
        return results

    def download_all(self, url: str, root_dir: str):
        """
        Parse country/kind/id -> download static and dynamic artwork.
        Create a ZIP. Return:
          subfolder_path, zip_path, info, downloaded_files
        where downloaded_files = [(filepath, is_video, resolution), ...]
        """
        print(f"[Downloader] Start handling URL: {url}")

        # Modified regex to handle numeric IDs or pl.* IDs (pl.rp, pl.u, etc.)
        match = re.search(r"apple\.com\/(\w\w)\/(playlist|album|artist)\/.+\/(\d+|pl\..+)", url)
        if not match:
            raise ValueError("Invalid Apple Music URL (cannot parse country/kind/id).")

        country, kind_singular, id_ = match.groups()
        # The Apple Music API expects: if we see 'playlist' -> kind=playlist
        # if 'album' -> kind=album, if 'artist' -> kind=artist
        # that is consistent with your original usage.
        kind = kind_singular  # direct usage

        data_json = self.get_json(id_, country, kind)
        attr = data_json['data'][0]['attributes']

        name = ensure_utf8(attr.get('name', ''))
        artist = ensure_utf8(attr.get('artistName', '') or attr.get('curatorName', ''))
        release_date = ensure_utf8(attr.get('releaseDate', ''))
        last_modified = ensure_utf8(attr.get('lastModifiedDate', ''))

        info = {
            "id": id_,
            "country": country,
            "kind": kind,
            "name": name,
            "artist": artist,
            "release_date": release_date,
            "last_modified": last_modified,
            "url": url
        }

        # Use root_dir directly without creating subfolder
        safe_name = sanitize_filename_utf8(id_)
        
        downloaded_files = []

        # Download static artwork with unique filename
        artwork = attr.get('artwork')
        if artwork:
            art_url = self.fix_artwork_url(artwork['url'])
            static_filename = f"{safe_name}_cover.png"
            static_path = os.path.join(root_dir, static_filename)
            if self.download_static_artwork(art_url, static_path):
                resolution = get_media_resolution(static_path)
                downloaded_files.append((static_path, False, resolution))

        # Download dynamic covers with fallback
        m3u8_urls = self.get_m3u8_urls(data_json)
        for tag, m3u8_url in m3u8_urls:
            video_filename = f"{safe_name}_{tag}.mp4"
            video_path = os.path.join(root_dir, video_filename)
            saved_video = self.download_video(m3u8_url, video_path)
            if saved_video:
                resolution = get_media_resolution(video_path)
                downloaded_files.append((video_path, True, resolution))

        # Create ZIP
        zip_basename = sanitize_filename_utf8(ensure_utf8(name)) or "apple_music"
        zip_filename = f"{zip_basename}.zip"  # no date/time to avoid extra info
        zip_path = os.path.join(root_dir, zip_filename)
        print(f"[Downloader] Creating zip: {zip_path}")
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for filepath, _, _ in downloaded_files:
                filename = os.path.basename(filepath)
                zipf.write(filepath, arcname=filename)
        print("[Downloader] Zip created.")

        return root_dir, zip_path, info, downloaded_files


#########################
# 4) Async file sending (DEPRECATED - Now only sending ZIP)
#########################
# This function is no longer used as we only send ZIP files now
# Keeping it commented for reference
"""
async def send_file_async(client, chat_id, file_info):
    # Send file with resolution info in caption.
    filepath, is_video, resolution = file_info
    print(f"[{get_beijing_time()}][Sender] Start sending file: {filepath}")

    caption = f"Resolution: {resolution}" if resolution else ""

    thumb_path = None
    if is_video:
        thumb_path = filepath + ".jpg"
        try:
            subprocess.run([
                'ffmpeg', '-loglevel', 'quiet',
                '-y', '-ss', '1',
                '-i', filepath,
                '-vframes', '1',
                '-vf', 'scale=320:-1',  # maintain aspect ratio, width=320
                thumb_path
            ], check=True)
        except Exception as e:
            thumb_path = None
            print(f"[{get_beijing_time()}][Sender] Failed generating thumbnail for {filepath}: {e}")

    try:
        if is_video:
            await client.send_file(
                chat_id,
                file=filepath,
                caption=caption,
                thumb=thumb_path if thumb_path else None,
                attributes=[
                    DocumentAttributeVideo(
                        duration=0, w=0, h=0, supports_streaming=True
                    )
                ]
            )
        else:
            # First try to send as photo
            try:
                await client.send_file(
                    chat_id,
                    file=filepath,
                    caption=caption,
                    force_document=False
                )
            except RPCError as e:
                # If failed (possibly because file is too large), send as document
                if "10MB" in str(e):
                    print(f"[{get_beijing_time()}][Sender] File {filepath} too large for photo, sending as document")
                    await client.send_file(
                        chat_id,
                        file=filepath,
                        caption=caption,
                        force_document=True
                    )
                else:
                    raise  # If it's another error, continue to raise
        print(f"[{get_beijing_time()}][Sender] File sent successfully: {filepath}")
    except Exception as e:
        print(f"[{get_beijing_time()}][Sender] Error sending file {filepath}: {e}")
        raise
    finally:
        if thumb_path and os.path.exists(thumb_path):
            os.remove(thumb_path)
            print(f"[{get_beijing_time()}][Sender] Removed thumbnail: {thumb_path}")
"""


#########################
# 5) Audio Processing Functions
#########################
async def process_audio_speed(audio_path: str, speed: float, output_path: str, keep_metadata: bool = True) -> bool:
    """
    Process audio with speed change using sox.
    Speed > 1 = faster (higher pitch), Speed < 1 = slower (lower pitch)
    Changes both tempo and pitch together (like tape speed change).
    When keep_metadata is True, we replace only the audio stream in the original file.
    """
    try:
        print(f"[{get_beijing_time()}][AudioProcess] Processing audio with speed {speed}x using sox (tempo + pitch)")
        
        # Create a temporary file for sox output
        temp_audio = audio_path + '.sox_temp.wav'
        
        # Use sox speed effect to change both tempo and pitch
        # Output to WAV to avoid any codec issues
        cmd_sox = ['sox', audio_path, temp_audio, 'speed', str(speed)]
        result = subprocess.run(cmd_sox, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"[{get_beijing_time()}][AudioProcess] Audio processed successfully with sox")
            
            if keep_metadata and os.path.exists(temp_audio):
                # Use ffmpeg to replace audio stream in original file
                # This preserves EVERYTHING except the audio stream itself
                cmd_replace = [
                    'ffmpeg', '-loglevel', 'error',
                    '-i', audio_path,    # original file (for container, metadata, artwork)
                    '-i', temp_audio,    # sox output (new audio)
                    '-map', '1:a',       # audio from sox output
                    '-map', '0:v?',      # video/artwork from original if exists
                    '-map_metadata', '0', # all metadata from original
                    '-c:v', 'copy',      # copy video/artwork without re-encoding
                    '-c:a', 'copy',      # copy audio without re-encoding (if possible)
                    '-id3v2_version', '3',
                    '-write_id3v1', '1',
                    '-y', output_path
                ]
                
                # If output format needs specific codec, adjust
                if output_path.lower().endswith('.mp3'):
                    # MP3 needs re-encoding from WAV
                    cmd_replace[cmd_replace.index('-c:a') + 1] = 'libmp3lame'
                    cmd_replace.insert(cmd_replace.index('-c:a') + 2, '-b:a')
                    cmd_replace.insert(cmd_replace.index('-b:a') + 1, '320k')
                elif output_path.lower().endswith('.aac') or output_path.lower().endswith('.m4a'):
                    cmd_replace[cmd_replace.index('-c:a') + 1] = 'aac'
                    cmd_replace.insert(cmd_replace.index('-c:a') + 2, '-b:a')
                    cmd_replace.insert(cmd_replace.index('-b:a') + 1, '256k')
                
                replace_result = subprocess.run(cmd_replace, capture_output=True, text=True)
                
                # Clean up temp file
                if os.path.exists(temp_audio):
                    os.remove(temp_audio)
                
                if replace_result.returncode == 0:
                    print(f"[{get_beijing_time()}][AudioProcess] Audio stream replaced successfully, all metadata preserved")
                    return True
                else:
                    print(f"[{get_beijing_time()}][AudioProcess] Failed to replace audio stream: {replace_result.stderr}")
                    # If replacement failed, just use sox output without metadata
                    if os.path.exists(temp_audio):
                        os.rename(temp_audio, output_path)
                        return True
            else:
                # If not preserving metadata, just rename the temp file
                if os.path.exists(temp_audio):
                    os.rename(temp_audio, output_path)
                    return True
            
            return True
        else:
            print(f"[{get_beijing_time()}][AudioProcess] sox failed: {result.stderr}")
            # Clean up temp file if exists
            if os.path.exists(temp_audio):
                os.remove(temp_audio)
                
            # Fallback to ffmpeg if sox fails
            print(f"[{get_beijing_time()}][AudioProcess] Falling back to ffmpeg...")
            
            sample_rate = 44100
            new_rate = int(sample_rate * speed)
            
            cmd_ffmpeg = [
                'ffmpeg', '-loglevel', 'error',
                '-i', audio_path,
                '-af', f'asetrate={new_rate},aresample={sample_rate}',
                '-y', output_path
            ]
            
            if keep_metadata:
                cmd_ffmpeg[5:5] = [
                    '-map', '0',
                    '-map_metadata', '0',
                    '-id3v2_version', '3',
                    '-write_id3v1', '1',
                    '-c:v', 'copy'
                ]
            
            result_ffmpeg = subprocess.run(cmd_ffmpeg, capture_output=True, text=True)
            return result_ffmpeg.returncode == 0
            
    except Exception as e:
        print(f"[{get_beijing_time()}][AudioProcess] Error processing audio: {e}")
        return False


async def concatenate_audio_files(audio_files: list, output_path: str) -> bool:
    """
    Concatenate multiple audio files using sox.
    Preserves all metadata from the first file by replacing only its audio stream.
    """
    try:
        print(f"[{get_beijing_time()}][AudioConcat] Concatenating {len(audio_files)} audio files with sox")
        
        # Create a temporary file for sox output
        temp_concat = output_path + '.sox_temp.wav'
        
        # Use sox to concatenate audio files to WAV
        cmd_sox = ['sox'] + audio_files + [temp_concat]
        result = subprocess.run(cmd_sox, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"[{get_beijing_time()}][AudioConcat] Audio concatenated successfully with sox")
            
            # Use ffmpeg to replace audio stream in first file with concatenated audio
            # This preserves ALL metadata, artwork, etc. from the first file
            if os.path.exists(temp_concat) and len(audio_files) > 0:
                cmd_replace = [
                    'ffmpeg', '-loglevel', 'error',
                    '-i', audio_files[0],  # first file (for container, metadata, artwork)
                    '-i', temp_concat,     # sox concatenated audio
                    '-map', '1:a',         # audio from sox output
                    '-map', '0:v?',        # video/artwork from first file if exists
                    '-map_metadata', '0',  # all metadata from first file
                    '-c:v', 'copy',        # copy video/artwork without re-encoding
                    '-c:a', 'copy',        # copy audio without re-encoding (if possible)
                    '-id3v2_version', '3',
                    '-write_id3v1', '1',
                    '-y', output_path
                ]
                
                # If output format needs specific codec, adjust
                if output_path.lower().endswith('.mp3'):
                    # MP3 needs re-encoding from WAV
                    cmd_replace[cmd_replace.index('-c:a') + 1] = 'libmp3lame'
                    cmd_replace.insert(cmd_replace.index('-c:a') + 2, '-b:a')
                    cmd_replace.insert(cmd_replace.index('-b:a') + 1, '320k')
                elif output_path.lower().endswith('.aac') or output_path.lower().endswith('.m4a'):
                    cmd_replace[cmd_replace.index('-c:a') + 1] = 'aac'
                    cmd_replace.insert(cmd_replace.index('-c:a') + 2, '-b:a')
                    cmd_replace.insert(cmd_replace.index('-b:a') + 1, '256k')
                elif output_path.lower().endswith('.flac'):
                    cmd_replace[cmd_replace.index('-c:a') + 1] = 'flac'
                
                replace_result = subprocess.run(cmd_replace, capture_output=True, text=True)
                
                # Clean up temp file
                if os.path.exists(temp_concat):
                    os.remove(temp_concat)
                
                if replace_result.returncode == 0:
                    print(f"[{get_beijing_time()}][AudioConcat] Audio streams merged successfully, all metadata from first file preserved")
                    return True
                else:
                    print(f"[{get_beijing_time()}][AudioConcat] Failed to merge with metadata: {replace_result.stderr}")
                    # If replacement failed but we have concatenated audio, rename it
                    if os.path.exists(temp_concat):
                        os.rename(temp_concat, output_path)
                        return True
            else:
                # If no metadata preservation needed, just rename
                if os.path.exists(temp_concat):
                    os.rename(temp_concat, output_path)
                    return True
            
            return False
        else:
            print(f"[{get_beijing_time()}][AudioConcat] sox failed: {result.stderr}")
            # Clean up temp file if exists
            if os.path.exists(temp_concat):
                os.remove(temp_concat)
                
            # Fallback to ffmpeg
            print(f"[{get_beijing_time()}][AudioConcat] Falling back to ffmpeg...")
            
            # Create a temporary file list for ffmpeg concat
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                for audio_file in audio_files:
                    f.write(f"file '{audio_file}'\n")
                list_file = f.name
            
            try:
                cmd_ffmpeg = [
                    'ffmpeg', '-loglevel', 'error',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', list_file,
                    '-map_metadata', '0',  # preserve metadata from first file
                    '-id3v2_version', '3',
                    '-write_id3v1', '1',
                    '-c', 'copy',
                    '-y', output_path
                ]
                
                result_ffmpeg = subprocess.run(cmd_ffmpeg, capture_output=True, text=True)
                return result_ffmpeg.returncode == 0
            finally:
                # Clean up temp file
                if os.path.exists(list_file):
                    os.remove(list_file)
            
    except Exception as e:
        print(f"[{get_beijing_time()}][AudioConcat] Error concatenating audio: {e}")
        return False


async def convert_audio_format(input_path: str, output_format: str) -> str:
    """
    Convert audio file to different format using sox.
    When converting, preserves all metadata by replacing only the audio stream.
    Returns the output path if successful, None otherwise.
    """
    try:
        print(f"[{get_beijing_time()}][AudioConvert] Converting audio to {output_format} using sox")
        
        # Create output filename
        base_name = os.path.splitext(safe_basename(input_path))[0]
        base_name = clean_filename_for_output(base_name)
        output_filename = sanitize_filename_utf8(f"{base_name}.{output_format}")
        output_path = safe_path_join(safe_dirname(input_path), output_filename)
        
        # Create a temporary file for sox output
        # Always use WAV as intermediate format for best compatibility
        temp_audio = input_path + '.sox_temp.wav'
        
        # First convert to WAV with sox
        cmd_sox = ['sox', input_path, temp_audio]
        
        # Add any format-specific preprocessing if needed
        if output_format == 'mp3' and input_path.lower().endswith('.mp3'):
            # If converting MP3 to MP3, maintain quality
            cmd_sox.extend(['-b', '16'])
        elif output_format == 'wav':
            # For WAV output, specify bit depth
            cmd_sox.extend(['-b', '16', '-r', '44100'])
        
        result = subprocess.run(cmd_sox, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"[{get_beijing_time()}][AudioConvert] Audio converted to intermediate format with sox")
            
            # Now use ffmpeg to create final output with metadata from original
            if os.path.exists(temp_audio):
                cmd_replace = [
                    'ffmpeg', '-loglevel', 'error',
                    '-i', input_path,     # original file (for metadata, artwork)
                    '-i', temp_audio,     # sox output (new audio)
                    '-map', '1:a',        # audio from sox output
                    '-map', '0:v?',       # video/artwork from original if exists
                    '-map_metadata', '0', # all metadata from original
                    '-c:v', 'copy',       # copy video/artwork without re-encoding
                    '-id3v2_version', '3',
                    '-write_id3v1', '1',
                    '-y', output_path
                ]
                
                # Set appropriate audio codec for output format
                if output_format == 'mp3':
                    cmd_replace.insert(-2, '-acodec')
                    cmd_replace.insert(-2, 'libmp3lame')
                    cmd_replace.insert(-2, '-b:a')
                    cmd_replace.insert(-2, '320k')
                elif output_format == 'aac' or output_format == 'm4a':
                    cmd_replace.insert(-2, '-acodec')
                    cmd_replace.insert(-2, 'aac')
                    cmd_replace.insert(-2, '-b:a')
                    cmd_replace.insert(-2, '256k')
                elif output_format == 'flac':
                    cmd_replace.insert(-2, '-acodec')
                    cmd_replace.insert(-2, 'flac')
                    cmd_replace.insert(-2, '-compression_level')
                    cmd_replace.insert(-2, '8')
                elif output_format == 'wav':
                    cmd_replace.insert(-2, '-acodec')
                    cmd_replace.insert(-2, 'pcm_s16le')
                    cmd_replace.insert(-2, '-ar')
                    cmd_replace.insert(-2, '44100')
                
                replace_result = subprocess.run(cmd_replace, capture_output=True, text=True)
                
                # Clean up temp file
                if os.path.exists(temp_audio):
                    os.remove(temp_audio)
                
                if replace_result.returncode == 0:
                    print(f"[{get_beijing_time()}][AudioConvert] Format converted successfully with all metadata preserved")
                    return output_path
                else:
                    print(f"[{get_beijing_time()}][AudioConvert] Failed to create final output with metadata: {replace_result.stderr}")
                    # If metadata preservation failed, try direct sox conversion
                    cmd_sox_direct = ['sox', input_path]
                    if output_format == 'mp3':
                        cmd_sox_direct.extend(['-C', '320'])
                    elif output_format == 'flac':
                        cmd_sox_direct.extend(['-C', '8'])
                    cmd_sox_direct.append(output_path)
                    
                    result_direct = subprocess.run(cmd_sox_direct, capture_output=True, text=True)
                    if result_direct.returncode == 0:
                        return output_path
            
            return None
        else:
            print(f"[{get_beijing_time()}][AudioConvert] sox failed: {result.stderr}")
            # Clean up temp file if exists
            if os.path.exists(temp_audio):
                os.remove(temp_audio)
                
            # Fallback to ffmpeg
            print(f"[{get_beijing_time()}][AudioConvert] Falling back to ffmpeg...")
            
            cmd_ffmpeg = [
                'ffmpeg', '-loglevel', 'error',
                '-i', input_path,
                '-map', '0',
                '-map_metadata', '0',
                '-id3v2_version', '3',
                '-write_id3v1', '1',
                '-c:v', 'copy',
                '-y', output_path
            ]
            
            # Format-specific audio settings for ffmpeg
            if output_format == 'mp3':
                cmd_ffmpeg[7:7] = ['-acodec', 'libmp3lame', '-ab', '320k']
            elif output_format == 'aac':
                cmd_ffmpeg[7:7] = ['-acodec', 'aac', '-ab', '256k']
            elif output_format == 'flac':
                cmd_ffmpeg[7:7] = ['-acodec', 'flac']
            elif output_format == 'wav':
                cmd_ffmpeg[7:7] = ['-acodec', 'pcm_s16le', '-ar', '44100', '-ac', '2']
            
            result_ffmpeg = subprocess.run(cmd_ffmpeg, capture_output=True, text=True)
            
            if result_ffmpeg.returncode == 0:
                return output_path
            else:
                return None
            
    except Exception as e:
        print(f"[{get_beijing_time()}][AudioConvert] Error converting audio: {e}")
        return None


async def convert_video_format(input_path: str, output_format: str) -> str:
    """
    Convert video file to different format using ffmpeg.
    Returns the output path if successful, None otherwise.
    """
    try:
        print(f"[{get_beijing_time()}][VideoConvert] Converting video to {output_format}")
        
        # Create output filename
        base_name = os.path.splitext(safe_basename(input_path))[0]
        base_name = clean_filename_for_output(base_name)
        output_filename = sanitize_filename_utf8(f"{base_name}.{output_format}")
        output_path = safe_path_join(safe_dirname(input_path), output_filename)
        
        # Use ffmpeg to convert
        cmd = [
            'ffmpeg', '-loglevel', 'quiet',
            '-y', '-i', input_path
        ]
        
        # Format-specific settings
        if output_format == 'mp4':
            cmd.extend(['-c:v', 'libx264', '-preset', 'medium', '-crf', '23'])
        elif output_format == 'webm':
            cmd.extend(['-c:v', 'libvpx-vp9', '-crf', '30', '-b:v', '0'])
        elif output_format == 'avi':
            cmd.extend(['-c:v', 'libxvid', '-qscale:v', '3'])
        elif output_format == 'mov':
            cmd.extend(['-c:v', 'libx264', '-preset', 'medium', '-crf', '23'])
        elif output_format == 'mkv':
            cmd.extend(['-c:v', 'copy', '-c:a', 'copy'])
        
        cmd.append(output_path)
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"[{get_beijing_time()}][VideoConvert] Video converted successfully to: {output_path}")
            return output_path
        else:
            print(f"[{get_beijing_time()}][VideoConvert] ffmpeg failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"[{get_beijing_time()}][VideoConvert] Error converting video: {e}")
        return None


#########################
# 6) Video to Audio Extraction
#########################
async def extract_audio_from_video(video_path: str, output_path: str) -> bool:
    """
    Extract audio from video file using ffmpeg.
    Preserves all audio metadata.
    Returns True if successful, False otherwise.
    """
    try:
        print(f"[{get_beijing_time()}][AudioExtract] Extracting audio from: {video_path}")
        
        # Use ffmpeg to extract audio with metadata preservation
        cmd = [
            'ffmpeg', '-loglevel', 'error',
            '-i', video_path,
            '-vn',  # no video
            '-acodec', 'libmp3lame',  # MP3 codec
            '-ab', '320k',  # 320kbps bitrate
            '-ar', '44100',  # 44.1kHz sample rate
            '-map_metadata', '0',  # Copy all metadata
            '-id3v2_version', '3',
            '-write_id3v1', '1',
            '-y', output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"[{get_beijing_time()}][AudioExtract] Audio extracted successfully with metadata preserved")
            return True
        else:
            print(f"[{get_beijing_time()}][AudioExtract] ffmpeg failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"[{get_beijing_time()}][AudioExtract] Error extracting audio: {e}")
        return False


#########################
# 7) Event Handlers
#########################
@client.on(events.NewMessage(pattern=r'(http[s]?://.*apple\.com.*)'))
async def handler(event):
    """
    When receiving an Apple Music link:
      1. Log/update user info in Supabase
      2. Download covers -> subfolder
      3. Send media files (show resolution only)
      4. Send ZIP (caption: basic info + button to open Apple Music)
      5. Cleanup
      6. Delete progress/original messages after a delay
    """
    chat_id = event.chat_id
    sender_id = event.sender_id
    sender = await event.get_sender()
    username = sender.username if sender.username else ""
    url = event.pattern_match.group(1).strip()

    print(f"[{get_beijing_time()}][Bot] Handling Apple Music link from user {sender_id} (username={username}): {url}")

    # 1) Parse URL to get id, country, kind
    match = re.search(r"apple\.com\/(\w\w)\/(playlist|album|artist)\/.+\/(\d+|pl\..+)", url)
    if not match:
        error_msg = await event.reply("Invalid Apple Music URL format.")
        await asyncio.sleep(5)
        await error_msg.delete()
        await event.delete()
        return
    
    country, kind, id_ = match.groups()
    
    # 2) Check cache first
    print(f"[{get_beijing_time()}][Bot] Checking cache for {id_} ({country}, {kind})")
    cached = get_cached_file(id_, country, kind)
    
    if cached and cached['file_id']:
        print(f"[{get_beijing_time()}][Bot] Found in cache, sending cached file")
        progress_msg = await event.reply("Found in cache, sending...")
        
        # Build caption with cached info
        zip_caption = (
            f"ID: {id_}\n"
            f"Type: {kind}\n"
            f"Name: {ensure_utf8(cached['name']) if cached['name'] else 'N/A'}\n"
            f"Artist: {ensure_utf8(cached['artist']) if cached['artist'] else 'N/A'}\n"
        )
        if cached['release_date']:
            zip_caption += f"Release Date: {ensure_utf8(cached['release_date'])}\n"
        if cached['last_modified']:
            zip_caption += f"Last Modified: {ensure_utf8(cached['last_modified'])}\n"
        zip_caption += f"Country: {country}"
        
        # Button for Apple Music
        buttons = [[Button.url(text="Open in Apple Music", url=cached['url'] or url)]]
        
        try:
            await client.send_file(
                chat_id,
                cached['file_id'],
                caption=zip_caption,
                force_document=True,
                buttons=buttons
            )
            print(f"[{get_beijing_time()}][Bot] Cached file sent successfully")
            await progress_msg.delete()
            await event.delete()
            return
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error sending cached file: {e}")
            print(f"[{get_beijing_time()}][Bot] Will download fresh copy")
            await progress_msg.edit("Cache expired, downloading fresh copy...")
    
    # 3) If not cached or cache failed, download fresh
    global global_downloader
    if global_downloader is None:
        global_downloader = AppleMusicDownloaderExtended()
    downloader = global_downloader
    downloads_root = "/home/<USER>/working/apple_music_downloads"
    if not os.path.exists(downloads_root):
        os.makedirs(downloads_root)

    if not cached:
        progress_msg = await event.reply("Processing your link, please wait...")
    # else we already have progress_msg from cache attempt

    subfolder_path, zip_path, info, downloaded_files = (None, None, None, [])
    try:
        subfolder_path, zip_path, info, downloaded_files = downloader.download_all(url, downloads_root)
    except Exception as e:
        print(f"[{get_beijing_time()}][Bot] Download error: {e}")
        error_msg = await event.reply("Invalid link or cannot handle it for now. Please check the link.")
        # Wait and remove
        await asyncio.sleep(5)
        await error_msg.delete()
        await event.delete()
        return

    print(f"[{get_beijing_time()}][Bot] Downloaded files: {downloaded_files}")
    
    # 3) Send ZIP directly (no individual media files)
    zip_caption = (
        f"ID: {ensure_utf8(info['id'])}\n"
        f"Type: {ensure_utf8(info['kind'])}\n"
        f"Name: {ensure_utf8(info['name'])}\n"
        f"Artist: {ensure_utf8(info['artist'])}\n"
    )
    if info['release_date']:
        zip_caption += f"Release Date: {ensure_utf8(info['release_date'])}\n"
    if info['last_modified']:
        zip_caption += f"Last Modified: {ensure_utf8(info['last_modified'])}\n"
    zip_caption += f"Country: {ensure_utf8(info['country'])}\n"
    
    # Add file count info
    file_count = len(downloaded_files)
    zip_caption += f"\nContains {file_count} file{'s' if file_count != 1 else ''}"

    # Button for Apple Music
    buttons = [[Button.url(text="Open in Apple Music", url=info['url'])]]

    await progress_msg.edit("Uploading ZIP file...")

    try:
        sent_file = await client.send_file(
            chat_id,
            zip_path,
            caption=zip_caption,
            force_document=True,
            buttons=buttons
        )
        print(f"[{get_beijing_time()}][Bot] ZIP sent successfully: {zip_path}")
        
        # Save to cache
        if sent_file and sent_file.file and hasattr(sent_file.file, 'id'):
            file_id = sent_file.file.id
            file_size = os.path.getsize(zip_path) if os.path.exists(zip_path) else None
            save_to_cache(info, file_id, file_size)
            print(f"[{get_beijing_time()}][Bot] Saved to cache with file_id: {file_id}")
        
    except Exception as e:
        print(f"[{get_beijing_time()}][Bot] Error while sending ZIP: {e}")
        error_msg = await progress_msg.edit("Error sending ZIP file. Please try again later.")
        await asyncio.sleep(5)
        await error_msg.delete()
        await event.delete()
        return

    # 4) Cleanup
    print(f"[{get_beijing_time()}][Bot] Cleaning up files...")
    # Clean up individual files since they're already in the ZIP
    for filepath, _, _ in downloaded_files:
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
            except Exception as e:
                print(f"[{get_beijing_time()}][Bot] Error removing {filepath}: {e}")
    
    # Remove ZIP after sending
    if zip_path and os.path.exists(zip_path):
        try:
            os.remove(zip_path)
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error removing ZIP {zip_path}: {e}")

    # 5) Final cleanup - delete progress message and original message
    await progress_msg.delete()
    await event.delete()
    print(f"[{get_beijing_time()}][Bot] Task completed successfully")


@client.on(events.NewMessage(func=lambda e: e.audio or (e.document and e.document.mime_type and e.document.mime_type.startswith('audio/'))))
async def audio_handler(event):
    """
    Handle audio files (including forwarded audio).
    Provide options for concatenation and speed adjustment.
    """
    chat_id = event.chat_id
    sender_id = event.sender_id
    sender = await event.get_sender()
    username = sender.username if sender.username else ""
    
    print(f"[{get_beijing_time()}][Bot] Received audio from user {sender_id} (username={username})")
    
    # Check if we're in a concatenation session
    session = audio_sessions[sender_id]
    if session["waiting_for_next"]:
        # Download this audio and add to session
        progress_msg = await event.reply("Processing audio for concatenation...")
        
        try:
            # Get original filename from the message
            original_filename = None
            if event.document:
                original_filename = event.document.attributes[0].file_name if event.document.attributes else None
            
            audio_path = await event.download_media()
            if not audio_path:
                raise Exception("Failed to download audio")
                
            # Rename file with UTF-8 encoded name if original filename exists
            if original_filename:
                original_filename = ensure_utf8(original_filename)
                # Ensure the filename is filesystem-safe
                safe_filename = sanitize_filename(original_filename)
                new_path = os.path.join(os.path.dirname(audio_path), safe_filename)
                try:
                    # Use UTF-8 encoding for file operations
                    if isinstance(new_path, str):
                        new_path = new_path.encode('utf-8', errors='surrogateescape').decode('utf-8', errors='surrogateescape')
                    os.rename(audio_path, new_path)
                    audio_path = new_path
                    print(f"[{get_beijing_time()}][Bot] Renamed to: {safe_filename}")
                except Exception as e:
                    print(f"[{get_beijing_time()}][Bot] Could not rename file: {e}")
            
            # Check file size
            file_size_gb = get_file_size_gb(audio_path)
            if file_size_gb > 2.0:
                os.remove(audio_path)
                raise Exception(f"File too large ({file_size_gb:.2f} GB). Maximum allowed size is 2 GB.")
                
            session["files"].append(audio_path)
            print(f"[{get_beijing_time()}][Bot] Added audio to concatenation session: {audio_path} (Size: {file_size_gb:.2f} GB)")
            
            await progress_msg.delete()
            
            # Ask if user wants to add more
            buttons = [
                [
                    Button.inline("Add More", data=f"concat_continue_{sender_id}"),
                    Button.inline("Stop & Merge", data=f"concat_stop_{sender_id}")
                ]
            ]
            
            await event.reply(
                f"Received audio #{len(session['files'])}. Add more audio files?",
                buttons=buttons
            )
            
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error processing audio: {e}")
            await progress_msg.edit(f"❌ Error processing audio: {str(e)}")
            await asyncio.sleep(5)
            await progress_msg.delete()
        
        return
    
    # Not in concatenation session, show processing options
    buttons = [
        [
            Button.inline("Merge", data=f"concat_start_{sender_id}"),
            Button.inline("Speed", data=f"speed_menu_{sender_id}")
        ],
        [
            Button.inline("Convert Format", data=f"audio_format_menu_{sender_id}")
        ]
    ]
    
    # Download the audio first
    progress_msg = await event.reply("Processing audio...")
    
    try:
        # Get original filename from the message
        original_filename = None
        if event.document:
            for attr in event.document.attributes:
                if hasattr(attr, 'file_name'):
                    original_filename = attr.file_name
                    break
        
        # Download the audio file
        audio_path = await event.download_media()
        if not audio_path:
            raise Exception("Failed to download audio")
            
        print(f"[{get_beijing_time()}][Bot] Downloaded audio to: {audio_path}")
        
        # Rename to original filename if available
        if original_filename:
            safe_filename = sanitize_filename(original_filename)
            if safe_filename and not safe_filename.startswith(('audio_', 'voice_', 'video_')):
                new_path = os.path.join(os.path.dirname(audio_path), safe_filename)
                try:
                    import shutil
                    shutil.move(audio_path, new_path)
                    audio_path = new_path
                    print(f"[{get_beijing_time()}][Bot] Renamed to: {safe_filename}")
                except Exception as e:
                    print(f"[{get_beijing_time()}][Bot] Could not rename: {e}")
                    original_filename = None
        
        # Check file size
        file_size_gb = get_file_size_gb(audio_path)
        if file_size_gb > 2.0:
            os.remove(audio_path)
            raise Exception(f"File too large ({file_size_gb:.2f} GB). Maximum allowed size is 2 GB.")
            
        # Store the audio path for later processing
        session["current_audio"] = audio_path
        # Store original filename if rename was successful
        if original_filename:
            session["original_filename"] = original_filename
        print(f"[{get_beijing_time()}][Bot] Audio downloaded to: {audio_path} (Size: {file_size_gb:.2f} GB)")
        
        await progress_msg.delete()
        
        # Show options
        await event.reply(
            "Select audio processing option:",
            buttons=buttons
        )
        
    except Exception as e:
        print(f"[{get_beijing_time()}][Bot] Error processing audio: {e}")
        await progress_msg.edit(f"❌ Error processing audio: {str(e)}")
        await asyncio.sleep(5)
        await progress_msg.delete()


@client.on(events.CallbackQuery)
async def callback_handler(event):
    """Handle button clicks"""
    data = event.data.decode('utf-8')
    sender_id = event.sender_id
    session = audio_sessions[sender_id]
    
    print(f"[{get_beijing_time()}][Bot] Callback received: {data}")
    
    if data.startswith("concat_start_"):
        # Start concatenation session
        session["files"] = [session.get("current_audio")]
        session["waiting_for_next"] = True
        
        await event.edit("Please send the second audio file to merge.")
        
    elif data.startswith("concat_continue_"):
        # Continue waiting for more audio
        session["waiting_for_next"] = True
        await event.edit(f"Received {len(session['files'])} audio files. Please send the next audio file.")
        
    elif data.startswith("concat_stop_"):
        # Stop and process concatenation
        session["waiting_for_next"] = False
        
        if len(session["files"]) < 2:
            await event.edit("At least 2 audio files are required for merging.")
            return
            
        await event.edit(f"Merging {len(session['files'])} audio files...")
        
        try:
            # Create output filename based on first file
            first_file = session["files"][0]
            first_file_name = os.path.splitext(safe_basename(first_file))[0]
            first_file_name = clean_filename_for_output(first_file_name)
            # Simplified filename format
            output_filename = sanitize_filename_utf8(f"{first_file_name}_merged.mp3")
            output_path = safe_path_join(safe_dirname(first_file), output_filename)
            
            # Concatenate audio files
            success = await concatenate_audio_files(session["files"], output_path)
            
            if not success:
                raise Exception("Failed to concatenate audio files")
            
            # Check output file size
            output_size_gb = get_file_size_gb(output_path)
            if output_size_gb > 1.95:
                raise Exception(f"Output file too large ({output_size_gb:.2f} GB). Please consider reducing the file size. Maximum allowed is 1.95 GB.")
            
            # Send the concatenated audio
            await event.client.send_file(
                event.chat_id,
                file=output_path,
                caption=f"🎵 Merged {len(session['files'])} files",
                force_document=False,  # Send as audio, not document
                file_name=output_filename  # Explicitly set the filename
            )
            
            await event.delete()
            
            # Clean up
            for audio_file in session["files"]:
                if os.path.exists(audio_file):
                    os.remove(audio_file)
            if os.path.exists(output_path):
                os.remove(output_path)
                
            # Reset session
            session["files"] = []
            
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error concatenating audio: {e}")
            await event.edit(f"❌ Merge failed: {str(e)}")
            
    elif data.startswith("speed_menu_"):
        # Show speed adjustment options
        buttons = []
        
        # 0.1 to 0.4
        row1 = []
        for speed in [0.1, 0.2, 0.3, 0.4]:
            row1.append(Button.inline(f"{speed}x", data=f"speed_{speed}_{sender_id}"))
        buttons.append(row1)
        
        # 0.5 to 0.9
        row2 = []
        for speed in [0.5, 0.6, 0.7, 0.8, 0.9]:
            row2.append(Button.inline(f"{speed}x", data=f"speed_{speed}_{sender_id}"))
        buttons.append(row2)
        
        # 1.0 to 1.5
        row3 = []
        for speed in [1.0, 1.1, 1.2, 1.3, 1.4, 1.5]:
            row3.append(Button.inline(f"{speed}x", data=f"speed_{speed}_{sender_id}"))
        buttons.append(row3)
        
        # 1.6 to 3.0
        row4 = []
        for speed in [1.6, 1.8, 2.0, 2.5, 3.0]:
            row4.append(Button.inline(f"{speed}x", data=f"speed_{speed}_{sender_id}"))
        buttons.append(row4)
        
        await event.edit("Select audio speed (pitch will be adjusted accordingly):", buttons=buttons)
        
    elif data.startswith("speed_"):
        # Process speed adjustment
        parts = data.split("_")
        speed = float(parts[1])
        
        await event.edit(f"Processing audio at {speed}x speed...")
        
        try:
            audio_path = session.get("current_audio")
            if not audio_path or not os.path.exists(audio_path):
                raise Exception("Audio file not found")
            
            # Create output filename
            current_filename = os.path.basename(audio_path)
            name_part, ext = os.path.splitext(current_filename)
            
            # Clean the filename to remove any previous speed indicators
            cleaned_name = clean_filename_for_output(name_part)
            
            # If extension is missing or not audio, default to .mp3
            if not ext or ext.lower() not in ['.mp3', '.wav', '.flac', '.m4a', '.aac', '.ogg']:
                ext = '.mp3'
                
            # Simple format: original_name_speed.ext
            output_filename = f"{cleaned_name}_{speed}x{ext}"
            output_path = os.path.join(os.path.dirname(audio_path), output_filename)
            
            print(f"[{get_beijing_time()}][Bot] Processing: {current_filename} -> {output_filename}")
            
            # Process audio speed
            success = await process_audio_speed(audio_path, speed, output_path)
            
            if not success:
                raise Exception("Failed to process audio speed")
            
            # Check output file size
            output_size_gb = get_file_size_gb(output_path)
            if output_size_gb > 1.95:
                raise Exception(f"Output file too large ({output_size_gb:.2f} GB). Please consider reducing the file size. Maximum allowed is 1.95 GB.")
            
            # Send the processed audio with the actual filename
            await event.client.send_file(
                event.chat_id,
                file=output_path,
                caption=f"🎵 Speed: {speed}x",
                force_document=False,  # Send as audio, not document
                file_name=output_filename  # Explicitly set the filename
            )
            
            await event.delete()
            
            # Clean up
            if os.path.exists(audio_path):
                os.remove(audio_path)
            if os.path.exists(output_path):
                os.remove(output_path)
                
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error processing audio speed: {e}")
            await event.edit(f"❌ Processing failed: {str(e)}")
            
    elif data.startswith("audio_format_menu_"):
        # Show audio format conversion options
        buttons = [
            [
                Button.inline("MP3", data=f"audio_convert_mp3_{sender_id}"),
                Button.inline("WAV", data=f"audio_convert_wav_{sender_id}"),
            ],
            [
                Button.inline("AAC", data=f"audio_convert_aac_{sender_id}"),
                Button.inline("FLAC", data=f"audio_convert_flac_{sender_id}")
            ]
        ]
        
        await event.edit("Select audio format to convert to:", buttons=buttons)
        
    elif data.startswith("audio_convert_"):
        # Process audio format conversion
        parts = data.split("_")
        format_type = parts[2]
        
        await event.edit(f"Converting audio to {format_type.upper()} format...")
        
        try:
            audio_path = session.get("current_audio")
            if not audio_path or not os.path.exists(audio_path):
                raise Exception("Audio file not found")
            
            # Convert audio format
            output_path = await convert_audio_format(audio_path, format_type)
            
            if not output_path:
                raise Exception("Failed to convert audio format")
            
            # Check output file size
            output_size_gb = get_file_size_gb(output_path)
            if output_size_gb > 1.95:
                raise Exception(f"Output file too large ({output_size_gb:.2f} GB). Please consider reducing the file size. Maximum allowed is 1.95 GB.")
            
            # Send the converted audio
            await event.client.send_file(
                event.chat_id,
                file=output_path,
                caption=f"🎵 Format: {format_type.upper()}",
                force_document=False,  # Send as audio, not document
                file_name=os.path.basename(output_path)  # Explicitly set the filename
            )
            
            await event.delete()
            
            # Clean up
            if os.path.exists(audio_path):
                os.remove(audio_path)
            if os.path.exists(output_path):
                os.remove(output_path)
                
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error converting audio format: {e}")
            await event.edit(f"❌ Conversion failed: {str(e)}")
            
    elif data.startswith("video_extract_audio_"):
        # Extract audio from video
        await event.edit("Extracting audio...")
        
        try:
            video_path = session.get("current_video")
            if not video_path or not os.path.exists(video_path):
                raise Exception("Video file not found")
            
            # Create output path for audio
            base_name = os.path.splitext(safe_basename(video_path))[0]
            base_name = clean_filename_for_output(base_name)
            # Simplified filename
            audio_filename = sanitize_filename_utf8(base_name + "_audio.mp3")
            audio_path = safe_path_join(safe_dirname(video_path), audio_filename)
            
            # Extract audio
            success = await extract_audio_from_video(video_path, audio_path)
            
            if not success:
                raise Exception("Failed to extract audio from video")
            
            # Check output file size
            output_size_gb = get_file_size_gb(audio_path)
            if output_size_gb > 1.95:
                raise Exception(f"Output file too large ({output_size_gb:.2f} GB). Please consider reducing the file size. Maximum allowed is 1.95 GB.")
            
            # Send the audio file
            await event.client.send_file(
                event.chat_id,
                file=audio_path,
                caption=f"🎵 Audio extracted",
                force_document=False,  # Send as audio, not document
                file_name=audio_filename  # Explicitly set the filename
            )
            
            await event.delete()
            
            # Clean up
            if os.path.exists(video_path):
                os.remove(video_path)
            if os.path.exists(audio_path):
                os.remove(audio_path)
                
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error extracting audio: {e}")
            await event.edit(f"❌ Extraction failed: {str(e)}")
            
    elif data.startswith("video_format_menu_"):
        # Show video format conversion options
        buttons = [
            [
                Button.inline("MP4", data=f"video_convert_mp4_{sender_id}"),
                Button.inline("WebM", data=f"video_convert_webm_{sender_id}"),
                Button.inline("MKV", data=f"video_convert_mkv_{sender_id}")
            ],
            [
                Button.inline("AVI", data=f"video_convert_avi_{sender_id}"),
                Button.inline("MOV", data=f"video_convert_mov_{sender_id}")
            ]
        ]
        
        await event.edit("Select video format to convert to:", buttons=buttons)
        
    elif data.startswith("video_convert_"):
        # Process video format conversion
        parts = data.split("_")
        format_type = parts[2]
        
        await event.edit(f"Converting video to {format_type.upper()} format...")
        
        try:
            video_path = session.get("current_video")
            if not video_path or not os.path.exists(video_path):
                raise Exception("Video file not found")
            
            # Convert video format
            output_path = await convert_video_format(video_path, format_type)
            
            if not output_path:
                raise Exception("Failed to convert video format")
            
            # Check output file size
            output_size_gb = get_file_size_gb(output_path)
            if output_size_gb > 1.95:
                raise Exception(f"Output file too large ({output_size_gb:.2f} GB). Please consider reducing the file size. Maximum allowed is 1.95 GB.")
            
            # Send the converted video
            await event.client.send_file(
                event.chat_id,
                file=output_path,
                caption=f"🎬 Video format conversion complete! Format: {format_type.upper()}",
                file_name=os.path.basename(output_path),  # Explicitly set the filename
                attributes=[
                    DocumentAttributeVideo(
                        duration=0, w=0, h=0, supports_streaming=True
                    )
                ]
            )
            
            await event.delete()
            
            # Clean up
            if os.path.exists(video_path):
                os.remove(video_path)
            if os.path.exists(output_path):
                os.remove(output_path)
                
        except Exception as e:
            print(f"[{get_beijing_time()}][Bot] Error converting video format: {e}")
            await event.edit(f"❌ Conversion failed: {str(e)}")


@client.on(events.NewMessage(func=lambda e: e.video or (e.document and e.document.mime_type and e.document.mime_type.startswith('video/'))))
async def video_handler(event):
    """
    Handle video files (including forwarded videos).
    Provide options for audio extraction and format conversion.
    """
    chat_id = event.chat_id
    sender_id = event.sender_id
    sender = await event.get_sender()
    username = sender.username if sender.username else ""
    
    print(f"[{get_beijing_time()}][Bot] Received video from user {sender_id} (username={username})")
    
    # Download the video first
    progress_msg = await event.reply("Processing video...")
    
    try:
        # Get original filename from the message
        original_filename = None
        if event.document:
            for attr in event.document.attributes:
                if hasattr(attr, 'file_name'):
                    original_filename = attr.file_name
                    break
        
        video_path = await event.download_media()
        if not video_path:
            raise Exception("Failed to download video")
            
        # Rename file with UTF-8 encoded name if original filename exists
        if original_filename:
            original_filename = ensure_utf8(original_filename)
            new_path = os.path.join(os.path.dirname(video_path), original_filename)
            try:
                os.rename(video_path, new_path)
                video_path = new_path
            except Exception as e:
                print(f"[{get_beijing_time()}][Bot] Could not rename file: {e}")
        
        # Check file size
        file_size_gb = get_file_size_gb(video_path)
        if file_size_gb > 2.0:
            os.remove(video_path)
            raise Exception(f"File too large ({file_size_gb:.2f} GB). Maximum allowed size is 2 GB.")
            
        # Store the video path for later processing
        session = audio_sessions[sender_id]
        session["current_video"] = video_path
        # Also store original filename if available
        if original_filename:
            session["original_video_filename"] = original_filename
        print(f"[{get_beijing_time()}][Bot] Video downloaded to: {video_path} (Size: {file_size_gb:.2f} GB)")
        
        await progress_msg.delete()
        
        # Show options
        buttons = [
            [
                Button.inline("Extract Audio", data=f"video_extract_audio_{sender_id}"),
                Button.inline("Convert Format", data=f"video_format_menu_{sender_id}")
            ]
        ]
        
        await event.reply(
            "Select video processing option:",
            buttons=buttons
        )
        
    except Exception as e:
        print(f"[{get_beijing_time()}][Bot] Error processing video: {e}")
        await progress_msg.edit(f"❌ Error processing video: {str(e)}")
        await asyncio.sleep(5)
        await progress_msg.delete()


#########################
# 8) Main
#########################
def main():
    print(f"[{get_beijing_time()}][Bot] Running... waiting for Apple Music links.")
    client.run_until_disconnected()

if __name__ == "__main__":
    main()
