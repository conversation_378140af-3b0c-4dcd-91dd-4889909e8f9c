#!/usr/bin/env node
/**
 * 小红书数据获取主程序 - 持久浏览器版本
 * 
 * 版本: 3.7.0
 * 特性:
 * - 浏览器持久运行，不会关闭
 * - 每次循环只刷新页面，不重新创建
 * - 只监控hulu账号
 * - 监控点赞、收藏和订阅作者（sub=true）的作品
 * - 提取HTML预加载数据（前10-14条）和API动态数据
 * - 保存完整的签名链接（包含xsec_token和xsec_source）
 * - 自动保存登录状态
 * - 增量数据获取
 */

import { chromium } from 'playwright';
import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { Command } from 'commander';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
config();

// 设置日志文件
const logFile = path.join(__dirname, `xhs_log_${new Date().toISOString().replace(/[:.]/g, '-')}.log`);
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

// 保存原始console方法
const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info
};

// 重写console方法以同时写入文件
const writeLog = (type, args) => {
    const timestamp = new Date().toISOString();
    const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    
    // 写入文件
    logStream.write(`[${timestamp}] [${type.toUpperCase()}] ${message}\n`);
    
    // 调用原始console方法
    originalConsole[type](...args);
};

console.log = (...args) => writeLog('log', args);
console.error = (...args) => writeLog('error', args);
console.warn = (...args) => writeLog('warn', args);
console.info = (...args) => writeLog('info', args);

// 程序退出时关闭日志流
process.on('exit', () => {
    logStream.end();
});

process.on('SIGINT', () => {
    logStream.end();
    process.exit();
});

process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
    logStream.end();
    process.exit(1);
});

// Supabase配置
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// 初始化Supabase客户端
let supabase = null;
if (SUPABASE_URL && SUPABASE_KEY) {
    try {
        supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
        console.log("✅ Supabase客户端初始化成功");
    } catch (e) {
        console.log(`❌ Supabase客户端初始化失败: ${e}`);
        supabase = null;
    }
} else {
    console.log("⚠️ 未配置Supabase环境变量");
}

/**
 * 浏览器管理器 - 管理持久运行的浏览器实例
 */
class BrowserManager {
    constructor() {
        this.browsers = {};  // 存储所有浏览器实例
        this.isInitialized = false;  // 初始化状态标志
        this.isShuttingDown = false;  // 正在关闭标志
    }
    
    /**
     * 初始化所有账号的浏览器
     */
    async initialize(users) {
        // 总是先清理旧浏览器（即使已初始化）
        console.log("\n🧹 清理已存在的浏览器实例...");
        
        // 如果已经初始化，先关闭
        if (this.isInitialized) {
            console.log("  📌 发现已初始化的浏览器，先关闭...");
            await this.closeAll();
            this.isInitialized = false;
            this.isShuttingDown = false;  // 重置关闭标志
        }
        
        // 检查并清理可能存在的旧浏览器进程
        await this.cleanupExistingBrowsers();
        
        console.log("\n🚀 初始化持久浏览器实例...");
        
        for (const [userName, account] of Object.entries(users)) {
            console.log(`\n📱 初始化 ${userName} 的浏览器...`);
            
            const profileDir = `/home/<USER>/Desktop/browser_data_${userName}`;
            
            // 创建配置目录
            if (!fs.existsSync(profileDir)) {
                fs.mkdirSync(profileDir, { recursive: true });
            }
            
            // 创建持久化上下文（设置headless为true，正常运行不显示界面）
            const context = await chromium.launchPersistentContext(profileDir, {
                headless: true,  // 无头模式
                args: ['--no-sandbox'],
                viewport: { width: 1920, height: 1080 },
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0'
            });
            
            // 不在初始化时检查登录，只在需要时检查
            
            // 只存储context，不预先创建页面
            this.browsers[userName] = {
                context: context,
                profileDir: profileDir,
                userId: account.user_id
            };
            
            console.log(`  ✅ ${userName} 浏览器初始化完成`);
        }
        
        console.log("\n✅ 所有浏览器初始化完成");
        this.isInitialized = true;
    }
    
    /**
     * 检查并处理登录状态
     */
    async checkAndHandleLogin(context, userName) {
        console.log(`  🔍 检查 ${userName} 的登录状态...`);
        
        // 创建一个页面来检查登录状态
        const page = await context.newPage();
        
        try {
            // 访问用户主页
            const userUrl = `https://www.xiaohongshu.com/user/profile/60b1967c000000000101fd47`;
            await page.goto(userUrl, { waitUntil: 'domcontentloaded', timeout: 15000 });
            await page.waitForTimeout(2000);
            
            // 检查是否显示"七天内"或登录按钮
            const needLogin = await page.evaluate(() => {
                // 查找包含"七天内"的文本
                const allElements = document.querySelectorAll('*');
                let hasSevenDays = false;
                let hasLoginButton = false;
                
                for (const element of allElements) {
                    const text = element.textContent || '';
                    if (text.includes('七天内') && element.children.length === 0) {
                        hasSevenDays = true;
                    }
                    if ((text === '登录' || text.includes('登录')) && 
                        (element.tagName === 'BUTTON' || element.tagName === 'A' || 
                         element.className.includes('login'))) {
                        hasLoginButton = true;
                    }
                }
                
                return hasSevenDays || hasLoginButton;
            }).catch(() => false);
            
            if (needLogin) {
                console.log(`  ⚠️ 检测到需要登录`);
                
                // 点击登录按钮
                const loginClicked = await page.evaluate(() => {
                    const allElements = document.querySelectorAll('button, a, div[class*="login"]');
                    for (const element of allElements) {
                        const text = element.textContent || '';
                        if (text === '登录' || text.includes('登录')) {
                            element.click();
                            return true;
                        }
                    }
                    return false;
                }).catch(() => false);
                
                if (loginClicked) {
                    console.log(`  📱 已点击登录按钮，等待登录弹窗...`);
                    await page.waitForTimeout(3000);
                }
                
                // 保存截图
                const screenshotPath = `/tmp/xhs_login_${userName}_${Date.now()}.png`;
                await page.screenshot({ path: screenshotPath, fullPage: false });
                console.log(`  📸 已保存登录页面截图: ${screenshotPath}`);
                console.log(`  🔗 请查看截图并扫码登录`);
                
                // 等待用户确认
                console.log(`\n  ⏸️ 请扫码登录后按回车继续...`);
                
                // 使用readline等待用户输入
                const readline = (await import('readline')).default;
                const rl = readline.createInterface({
                    input: process.stdin,
                    output: process.stdout
                });
                
                await new Promise(resolve => {
                    rl.question('', () => {
                        rl.close();
                        resolve();
                    });
                });
                
                console.log(`  🔄 刷新页面检查登录状态...`);
                await page.reload({ waitUntil: 'domcontentloaded', timeout: 15000 });
                await page.waitForTimeout(2000);
                
                // 再次检查登录状态
                const stillNeedLogin = await page.evaluate(() => {
                    const allElements = document.querySelectorAll('*');
                    for (const element of allElements) {
                        const text = element.textContent || '';
                        if ((text.includes('七天内') && element.children.length === 0) ||
                            ((text === '登录' || text.includes('登录')) && 
                             (element.tagName === 'BUTTON' || element.tagName === 'A' || 
                              element.className.includes('login')))) {
                            return true;
                        }
                    }
                    return false;
                }).catch(() => false);
                
                if (stillNeedLogin) {
                    console.log(`  ❌ 登录失败，请重新运行程序并确保扫码成功`);
                    throw new Error('登录失败');
                } else {
                    console.log(`  ✅ 登录成功！`);
                }
            } else {
                console.log(`  ✅ 已登录`);
            }
            
        } finally {
            // 关闭检查页面
            await page.close();
        }
    }
    
    /**
     * 检查并清理现有浏览器
     */
    async cleanupExistingBrowsers() {
        // 检查是否有未正常关闭的浏览器
        if (Object.keys(this.browsers).length > 0) {
            console.log("  🧹 发现未关闭的浏览器实例，正在清理...");
            await this.closeAll();
        }
        
        // 检查系统中的Chromium进程
        try {
            const { execSync } = await import('child_process');
            let chromiumCount = '0';
            
            try {
                // 使用更精确的匹配，排除grep本身
                chromiumCount = execSync('pgrep -f "chromium.*--" | wc -l 2>/dev/null || echo "0"', { 
                    encoding: 'utf-8',
                    shell: true 
                }).trim();
            } catch (e) {
                // pgrep没找到进程时会返回非0退出码，这是正常的
                chromiumCount = '0';
            }
            
            if (parseInt(chromiumCount) > 0) {  // 如果有任何chromium进程
                console.log(`  ⚠️ 发现 ${chromiumCount} 个Chromium进程`);
                
                // 显示进程详情
                try {
                    const processInfo = execSync('ps aux | grep chromium | grep -v grep | head -5', { encoding: 'utf-8' }).trim();
                    if (processInfo) {
                        console.log(`  📋 进程详情（前5个）:`);
                        const lines = processInfo.split('\n');
                        lines.forEach(line => {
                            const parts = line.split(/\s+/);
                            const pid = parts[1];
                            const cmd = parts.slice(10).join(' ').substring(0, 80);
                            console.log(`    PID: ${pid} - ${cmd}...`);
                        });
                    }
                } catch (e) {
                    // 忽略错误
                }
                
                console.log(`  🔧 正在清理Playwright的Chromium进程...`);
                
                try {
                    // 先尝试优雅关闭
                    execSync('pkill -15 -f "chromium.*--test-type"', { 
                        encoding: 'utf-8',
                        stdio: 'ignore',
                        shell: true
                    });
                    
                    // 等待一下
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // 强制终止剩余的
                    execSync('pkill -9 -f "chromium.*--test-type"', { 
                        encoding: 'utf-8',
                        stdio: 'ignore',
                        shell: true
                    });
                } catch (e) {
                    // pkill在没有找到进程时会返回非0，这是正常的，忽略
                }
                
                // 再尝试清理所有chromium进程
                try {
                    execSync('pkill -9 -f chromium', { 
                        encoding: 'utf-8',
                        stdio: 'ignore',
                        shell: true
                    });
                } catch (e) {
                    // 忽略
                }
                
                console.log(`  ✅ 已发送清理信号`);
                
                // 等待进程完全结束
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 再次检查
                try {
                    const newCount = execSync('pgrep -f "chromium.*--" | wc -l 2>/dev/null || echo "0"', { 
                        encoding: 'utf-8',
                        shell: true 
                    }).trim();
                    if (parseInt(newCount) > 0) {
                        // 显示剩余进程
                        try {
                            const remainingInfo = execSync('ps aux | grep chromium | grep -v grep', { encoding: 'utf-8' }).trim();
                            if (remainingInfo) {
                                console.log(`  ⚠️ 仍有 ${newCount} 个Chromium进程未清理:`);
                                const lines = remainingInfo.split('\n');
                                lines.forEach(line => {
                                    const parts = line.split(/\s+/);
                                    const user = parts[0];
                                    const pid = parts[1];
                                    const cmd = parts.slice(10).join(' ').substring(0, 60);
                                    console.log(`    用户: ${user}, PID: ${pid}, 命令: ${cmd}...`);
                                });
                                console.log(`  💡 提示: 这些可能是其他程序的Chromium进程，或需要手动清理`);
                            }
                        } catch (e) {
                            console.log(`  ℹ️ 仍有 ${newCount} 个Chromium进程（可能是其他程序的）`);
                        }
                    } else {
                        console.log(`  ✅ 所有Chromium进程已清理完毕`);
                    }
                } catch (e) {
                    console.log(`  ✅ 所有Chromium进程已清理完毕`);
                }
            } else {
                console.log(`  ✅ 没有发现残留的Chromium进程`);
            }
        } catch (e) {
            console.log(`  ⚠️ 检查进程时出错: ${e.message}`);
        }
    }
    
    /**
     * 检查浏览器是否活跃
     */
    async checkBrowserHealth(userName) {
        if (!this.browsers[userName]) {
            return false;
        }
        
        try {
            const context = this.browsers[userName].context;
            // 尝试执行一个简单操作来检查浏览器是否响应
            await context.pages()[0].evaluate(() => true);
            return true;
        } catch (e) {
            console.log(`  ❌ ${userName} 浏览器无响应: ${e.message}`);
            return false;
        }
    }
    
    /**
     * 创建新页面（每次调用都创建新的）
     */
    async createPage(userName) {
        // 检查是否正在关闭
        if (this.isShuttingDown) {
            throw new Error('浏览器正在关闭，停止创建新页面');
        }
        
        if (!this.browsers[userName]) {
            throw new Error(`浏览器 ${userName} 未初始化`);
        }
        
        try {
            const context = this.browsers[userName].context;
            return await context.newPage();
        } catch (e) {
            if (e.message.includes('Target page, context or browser has been closed')) {
                throw new Error('浏览器已关闭');
            }
            throw e;
        }
    }
    
    /**
     * 获取指定用户的上下文
     */
    getContext(userName) {
        if (!this.browsers[userName]) {
            throw new Error(`浏览器 ${userName} 未初始化`);
        }
        return this.browsers[userName].context;
    }
    
    
    /**
     * 关闭所有浏览器
     */
    async closeAll() {
        this.isShuttingDown = true;  // 设置关闭标志
        console.log("\n🔚 关闭所有浏览器...");
        for (const [userName, browser] of Object.entries(this.browsers)) {
            try {
                await browser.context.close();
                console.log(`  ✅ ${userName} 浏览器已关闭`);
            } catch (e) {
                console.log(`  ⚠️ 关闭 ${userName} 浏览器失败: ${e.message}`);
            }
        }
        this.browsers = {};
        this.isInitialized = false;
    }
    
    /**
     * 重启指定用户的浏览器
     */
    async restartBrowser(userName, account) {
        console.log(`\n🔄 重启 ${userName} 的浏览器...`);
        
        // 先关闭旧的
        if (this.browsers[userName]) {
            try {
                await this.browsers[userName].context.close();
            } catch (e) {
                // 忽略错误
            }
            delete this.browsers[userName];
        }
        
        // 重新创建
        const profileDir = `/home/<USER>/Desktop/browser_data_${userName}`;
        
        const context = await chromium.launchPersistentContext(profileDir, {
            headless: true,  // 无头模式
            args: ['--no-sandbox'],
            viewport: { width: 1920, height: 1080 },
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0'
        });
        
        // 不检查登录状态，只在需要时检查
        
        this.browsers[userName] = {
            context: context,
            profileDir: profileDir,
            userId: account.user_id
        };
        
        console.log(`  ✅ ${userName} 浏览器重启完成`);
    }
}

/**
 * 数据获取器 - 使用持久浏览器获取数据
 */
class PersistentFetcher {
    constructor(browserManager, userName, userId, isFullMode = false) {
        this.browserManager = browserManager;
        this.userName = userName;
        this.userId = userId;
        this.isFullMode = isFullMode;  // 是否全量模式
        this.apiNotes = {};
        this.apiCount = 0;
    }
    
    /**
     * 检查并处理登录（在fetchData中使用）
     */
    async checkAndHandleLoginForFetcher(page) {
        console.log(`  🔍 检查页面登录状态...`);
        
        try {
            // 先访问用户主页检查登录状态
            const userUrl = `https://www.xiaohongshu.com/user/profile/${this.userId}`;
            await page.goto(userUrl, { waitUntil: 'domcontentloaded', timeout: 15000 });
            await page.waitForTimeout(2000);
            
            // 检查是否显示"七天内"或登录按钮
            const needLogin = await page.evaluate(() => {
                const allElements = document.querySelectorAll('*');
                for (const element of allElements) {
                    const text = element.textContent || '';
                    if ((text.includes('七天内') && element.children.length === 0) ||
                        ((text === '登录' || text.includes('登录')) && 
                         (element.tagName === 'BUTTON' || element.tagName === 'A' || 
                          element.className.includes('login')))) {
                        return true;
                    }
                }
                return false;
            }).catch(() => false);
            
            if (needLogin) {
                console.log(`  ⚠️ 检测到需要登录`);
                console.log(`\n  ⏸️ 按回车点击登录按钮...`);
                
                // 第一次回车：等待用户准备
                const readline = (await import('readline')).default;
                let rl = readline.createInterface({
                    input: process.stdin,
                    output: process.stdout
                });
                
                await new Promise(resolve => {
                    rl.question('', () => {
                        rl.close();
                        resolve();
                    });
                });
                
                // 点击登录按钮
                const loginClicked = await page.evaluate(() => {
                    const allElements = document.querySelectorAll('button, a, div[class*="login"]');
                    for (const element of allElements) {
                        const text = element.textContent || '';
                        if (text === '登录' || text.includes('登录')) {
                            element.click();
                            return true;
                        }
                    }
                    return false;
                }).catch(() => false);
                
                if (loginClicked) {
                    console.log(`  📱 已点击登录按钮，等待登录弹窗...`);
                    await page.waitForTimeout(3000);
                }
                
                // 保存截图
                const screenshotPath = `/tmp/xhs_login_${this.userName}_${Date.now()}.png`;
                await page.screenshot({ path: screenshotPath, fullPage: false });
                console.log(`  📸 已保存登录页面截图: ${screenshotPath}`);
                console.log(`  🔗 请查看截图并扫码登录`);
                
                // 第二次回车：等待用户扫码完成
                console.log(`\n  ⏸️ 扫码完成后按回车刷新页面...`);
                
                rl = readline.createInterface({
                    input: process.stdin,
                    output: process.stdout
                });
                
                await new Promise(resolve => {
                    rl.question('', () => {
                        rl.close();
                        resolve();
                    });
                });
                
                console.log(`  🔄 刷新页面检查登录状态...`);
                await page.reload({ waitUntil: 'domcontentloaded', timeout: 15000 });
                await page.waitForTimeout(2000);
                
                // 再次检查登录状态
                const stillNeedLogin = await page.evaluate(() => {
                    const allElements = document.querySelectorAll('*');
                    for (const element of allElements) {
                        const text = element.textContent || '';
                        if ((text.includes('七天内') && element.children.length === 0) ||
                            ((text === '登录' || text.includes('登录')) && 
                             (element.tagName === 'BUTTON' || element.tagName === 'A' || 
                              element.className.includes('login')))) {
                            return true;
                        }
                    }
                    return false;
                }).catch(() => false);
                
                if (stillNeedLogin) {
                    console.log(`  ❌ 登录失败`);
                    return false;
                } else {
                    console.log(`  ✅ 登录成功！`);
                    return true;
                }
            } else {
                console.log(`  ✅ 已登录`);
                return true;
            }
        } catch (e) {
            console.log(`  ❌ 登录检查失败: ${e.message}`);
            return false;
        }
    }
    
    /**
     * 获取订阅作者列表
     */
    async getSubscribedAuthors() {
        if (!supabase) {
            console.log("⚠️ 数据库未连接，无法获取订阅作者");
            return [];
        }
        
        try {
            const { data, error } = await supabase
                .from('xhs_user')
                .select('userid, nickname')
                .eq('sub', true);
            
            if (error) throw error;
            
            if (data && data.length > 0) {
                console.log(`👥 找到 ${data.length} 个订阅作者`);
                return data;
            } else {
                console.log("ℹ️ 没有找到订阅作者");
                return [];
            }
        } catch (e) {
            console.log(`❌ 获取订阅作者失败: ${e.message}`);
            return [];
        }
    }
    
    /**
     * 从作者页面HTML提取预加载数据
     */
    async extractAuthorHtmlNotes(page) {
        try {
            const htmlContent = await page.content();
            
            // 查找__INITIAL_STATE__
            const stateMatch = htmlContent.match(/window\.__INITIAL_STATE__\s*=\s*({.*?})\s*<\/script>/s);
            if (!stateMatch) {
                return {};
            }
            
            // 解析JSON，处理undefined
            const stateJson = stateMatch[1].replace(/undefined/g, 'null');
            const initialState = JSON.parse(stateJson);
            
            // 作者页面的数据可能在不同位置
            let notes = [];
            if (initialState.user && initialState.user.notes) {
                // 可能是数组或数组的数组
                if (Array.isArray(initialState.user.notes[0])) {
                    notes = initialState.user.notes[0] || [];
                } else {
                    notes = initialState.user.notes || [];
                }
            }
            
            const htmlNotes = {};
            for (const noteWrapper of notes) {
                if (noteWrapper && noteWrapper.id) {
                    const noteCard = noteWrapper.noteCard || noteWrapper;
                    htmlNotes[noteWrapper.id] = {
                        note_id: noteWrapper.id,
                        display_title: noteCard.displayTitle || noteCard.title || '',
                        xsec_token: noteCard.xsecToken || '',
                        ...noteCard
                    };
                }
            }
            
            return htmlNotes;
        } catch (e) {
            return {};
        }
    }
    
    /**
     * 获取作者的作品（带重试机制）
     */
    async fetchAuthorWorks(authorId, authorName, retryCount = 0) {
        const maxRetries = 5;  // 增加到5次重试
        
        // 为每个作者创建新页面
        let page = null;
        try {
            page = await this.browserManager.createPage(this.userName);
            console.log(`  📄 为作者 ${authorName} 创建新标签页`);
            
            // 直接访问作者主页
            const authorUrl = `https://www.xiaohongshu.com/user/profile/${authorId}?tab=note`;
            console.log(`  🌐 访问作者主页: ${authorName}`);
            console.log(`  🔗 URL: ${authorUrl}`);
            if (retryCount > 0) {
                console.log(`  🔄 重试次数: ${retryCount}/${maxRetries}`);
            }
            
            // 设置API响应监听器
            let apiNotes = [];
            let hasMore = true;
            
            // 用于去重API请求日志
            const loggedUrls = new Set();
            
            const handleResponse = async (response) => {
                const url = response.url();
                // 监听作者作品API - 添加更多可能的API端点
                if (url.includes('/api/sns/web/v1/feed') || 
                    url.includes('/api/sns/web/v1/user_posted') ||
                    url.includes('/user/posted') ||
                    url.includes('/api/sns/web/v1/user/otherinfo')) {
                    
                    // 只记录不重复的API请求
                    const urlKey = url.substring(0, 100);
                    if (!loggedUrls.has(urlKey)) {
                        console.log(`  🔍 检测到API请求: ${urlKey}...`);
                        loggedUrls.add(urlKey);
                    }
                    
                    try {
                        if (response.status() === 200) {
                            const data = await response.json();
                            
                            // 尝试多种数据路径
                            let notes = [];
                            if (data.data) {
                                if (data.data.items) {
                                    notes = data.data.items;
                                } else if (data.data.notes) {
                                    notes = data.data.notes;
                                } else if (Array.isArray(data.data)) {
                                    notes = data.data;
                                }
                            }
                            
                            if (notes.length > 0) {
                                console.log(`  📡 API响应: 获取到 ${notes.length} 条作品`);
                                apiNotes = apiNotes.concat(notes);
                            }
                            
                            // 检查是否还有更多数据
                            hasMore = (data.data && data.data.has_more) || false;
                            if (!hasMore) {
                                console.log(`  📌 API返回has_more=false，已获取全部作品`);
                            }
                        }
                    } catch (e) {
                        console.log(`  ⚠️ 解析API响应失败: ${e.message}`);
                    }
                }
            };
            
            page.on('response', handleResponse);
            
            // 使用domcontentloaded替代networkidle，避免因持续请求导致超时
            await page.goto(authorUrl, { waitUntil: 'domcontentloaded', timeout: 15000 });
            await page.waitForTimeout(3000);
            
            // 先尝试提取HTML预加载数据
            let htmlNotes = {};
            try {
                htmlNotes = await this.extractAuthorHtmlNotes(page);
                if (Object.keys(htmlNotes).length > 0) {
                    console.log(`  📄 作者页HTML预加载: ${Object.keys(htmlNotes).length} 条`);
                    console.log(`  📝 HTML预加载作品详情（共${Object.keys(htmlNotes).length}条，全部打印）:`);
                    let count = 0;
                    for (const [noteId, note] of Object.entries(htmlNotes)) {
                        const title = note.display_title || note.title || '无标题';
                        const link = `https://www.xiaohongshu.com/explore/${noteId}${note.xsec_token ? `?xsec_token=${note.xsec_token}` : ''}`;
                        console.log(`    ${count + 1}. 标题: ${title}`);
                        console.log(`       链接: ${link}`);
                        count++;
                    }
                    console.log(`  ✅ HTML预加载${Object.keys(htmlNotes).length}条已全部打印`);
                }
            } catch (e) {
                // 如果提取失败，继续处理但不打印详情
                console.log(`  ⚠️ 提取HTML预加载数据失败: ${e.message}`);
            }
            
            // 尝试等待笔记列表加载（如果超时也继续）
            try {
                await page.waitForSelector('[class*="note-item"]', { timeout: 5000 });
            } catch (e) {
                console.log(`  ⚠️ 未找到笔记列表元素，尝试滚动加载`);
            }
            
            // 增量模式：先检查HTML预加载的数据是否都已存在
            if (!this.isFullMode && Object.keys(htmlNotes).length > 0) {
                const htmlNoteIds = Object.keys(htmlNotes);
                const dbCheck = await this.checkAuthorNotesInDatabase(htmlNoteIds);
                const allHtmlExist = htmlNoteIds.every(id => dbCheck[id]);
                
                if (allHtmlExist) {
                    console.log(`  ⏹️ 增量模式：HTML预加载的${htmlNoteIds.length}条作品都已存在，无需滚动获取更多`);
                    // 不需要滚动，直接使用已有数据
                } else {
                    // 有新作品，继续滚动获取更多
                    console.log(`  ℹ️ 增量模式：发现新作品，继续获取`);
                    
                    // 滚动加载更多数据
                    let scrollCount = 0;
                    let previousApiCount = 0;
                    let noNewDataCount = 0;
                    let shouldStop = false;
                    
                    while (hasMore && !shouldStop) {
                        scrollCount++;
                        await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
                        await page.waitForTimeout(2000);
                        
                        // 检查是否有新数据
                        if (apiNotes.length === previousApiCount) {
                            noNewDataCount++;
                            if (noNewDataCount >= 3) {
                                console.log(`  ⏹️ 连续3次滚动无新数据，已到达底部`);
                                break;
                            }
                        } else {
                            const oldCount = previousApiCount;
                            previousApiCount = apiNotes.length;
                            
                            // 增量模式：检查新获取的数据是否都已存在
                            if (!this.isFullMode && apiNotes.length > oldCount) {
                                // 获取最新一批数据进行检查
                                const latestBatch = apiNotes.slice(oldCount);
                                const noteIds = latestBatch.map(note => note.id || note.note_id).filter(id => id);
                                
                                if (noteIds.length > 0) {
                                    const dbCheck = await this.checkAuthorNotesInDatabase(noteIds);
                                    const allExist = noteIds.every(id => dbCheck[id]);
                                    
                                    if (allExist) {
                                        console.log(`  ⏹️ 增量模式：最新一批${noteIds.length}条作品都已存在，停止滚动`);
                                        shouldStop = true;
                                    }
                                }
                            }
                            
                            noNewDataCount = 0;
                        }
                        
                        if (scrollCount % 3 === 0) {
                            console.log(`  📜 已滚动 ${scrollCount} 次，API获取到 ${apiNotes.length} 条作品`);
                        }
                    }
                }
            } else if (this.isFullMode) {
                // 全量模式：滚动获取所有数据
                console.log(`  ℹ️ 全量模式：获取所有作品`);
                
                let scrollCount = 0;
                let previousApiCount = 0;
                let noNewDataCount = 0;
                
                while (hasMore) {
                    scrollCount++;
                    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
                    await page.waitForTimeout(2000);
                    
                    // 检查是否有新数据
                    if (apiNotes.length === previousApiCount) {
                        noNewDataCount++;
                        if (noNewDataCount >= 3) {
                            console.log(`  ⏹️ 连续3次滚动无新数据，已到达底部`);
                            break;
                        }
                    } else {
                        previousApiCount = apiNotes.length;
                        noNewDataCount = 0;
                    }
                    
                    if (scrollCount % 3 === 0) {
                        console.log(`  📜 已滚动 ${scrollCount} 次，API获取到 ${apiNotes.length} 条作品`);
                    }
                }
            }
            
            // 移除监听器
            page.removeListener('response', handleResponse);
            
            // 获取DOM中的笔记列表
            const domNotes = await page.evaluate(() => {
                const noteElements = document.querySelectorAll('[class*="note-item"]');
                const notesData = [];
                
                noteElements.forEach(element => {
                    try {
                        const linkElement = element.querySelector('a[href*="/explore/"]');
                        if (linkElement) {
                            const href = linkElement.getAttribute('href');
                            const noteId = href.split('/').pop().split('?')[0];
                            
                            const titleElement = element.querySelector('[class*="title"], [class*="desc"]');
                            const title = titleElement ? titleElement.textContent.trim() : '';
                            
                            notesData.push({
                                id: noteId,
                                title: title,
                                link: `https://www.xiaohongshu.com${href}`
                            });
                        }
                    } catch (e) {
                        // 忽略单个元素的错误
                    }
                });
                
                return notesData;
            });
            
            // 合并HTML预加载数据、API数据和DOM数据（去重）
            const allNotes = {};
            
            // 先添加HTML预加载的数据（通常更完整）
            for (const [noteId, note] of Object.entries(htmlNotes)) {
                allNotes[noteId] = {
                    id: noteId,
                    title: note.display_title || note.title || '无标题',
                    link: `https://www.xiaohongshu.com/explore/${noteId}${note.xsec_token ? `?xsec_token=${note.xsec_token}` : ''}`
                };
            }
            
            // 添加API数据
            for (const note of apiNotes) {
                const noteId = note.id || note.note_id;
                if (noteId && !allNotes[noteId]) {
                    // 构建带签名的链接
                    const xsecToken = note.xsec_token || '';
                    const xsecSource = note.xsec_source || '';
                    let link = `https://www.xiaohongshu.com/explore/${noteId}`;
                    
                    if (xsecToken && xsecSource) {
                        link += `?xsec_token=${xsecToken}&xsec_source=${xsecSource}`;
                    } else if (xsecToken) {
                        link += `?xsec_token=${xsecToken}`;
                    }
                    
                    allNotes[noteId] = {
                        id: noteId,
                        title: note.display_title || note.title || '无标题',
                        link: link
                    };
                }
            }
            
            // 最后添加DOM中的数据（可能有额外的）
            for (const note of domNotes) {
                if (!allNotes[note.id]) {
                    allNotes[note.id] = note;
                }
            }
            
            const notes = Object.values(allNotes);
            
            if (notes.length > 0) {
                console.log(`  📝 数据汇总: HTML(${Object.keys(htmlNotes).length})+ API(${apiNotes.length})+ DOM(${domNotes.length})= 共${notes.length}篇`);
                
                // 详细打印所有作者作品信息 - 确保完整打印，不省略
                console.log(`  📋 作者作品详情（共${notes.length}条，全部打印）:`);
                for (let i = 0; i < notes.length; i++) {
                    const note = notes[i];
                    const title = note.title || '无标题';
                    // 确保标题完整显示，不截断
                    console.log(`    ${i + 1}. 标题: ${title}`);
                    console.log(`       链接: ${note.link}`);
                }
                console.log(`  ✅ 已完整打印全部${notes.length}条作品`);
                
                // 更新到数据库
                for (const note of notes) {
                    await this.updateAuthorNoteToDatabase(note, authorId, authorName);
                }
                
                // 关闭页面
                await page.close();
                console.log(`  📄 关闭作者 ${authorName} 的标签页`);
                
                return notes.length;
            } else {
                console.log(`  ℹ️ 未获取到作品`);
                
                // 关闭页面
                await page.close();
                console.log(`  📄 关闭作者 ${authorName} 的标签页`);
                
                return 0;
            }
            
        } catch (e) {
            console.log(`  ❌ 获取作者作品失败: ${e.message}`);
            
            // 如果重试失败，先截图再关闭页面
            if (retryCount >= maxRetries) {
                console.log(`  ⚠️ 已达到最大重试次数(${maxRetries}次)，保存截图`);
                
                // 在关闭页面前截图
                try {
                    if (page && !page.isClosed()) {
                        const timestamp = Date.now();
                        const screenshotPath = path.join(process.cwd(), `xhs_author_failed_${authorName}_${timestamp}.png`);
                        await page.screenshot({ 
                            path: screenshotPath, 
                            fullPage: true 
                        });
                        console.log(`  📸 已保存失败页面截图: ${screenshotPath}`);
                    }
                } catch (screenshotError) {
                    console.log(`  ⚠️ 无法保存截图: ${screenshotError.message}`);
                }
            }
            
            // 关闭当前页面
            try {
                await page.close();
                console.log(`  📄 关闭失败的标签页`);
            } catch (closeError) {
                // 忽略关闭错误
            }
            
            // 如果是超时错误且还有重试次数，则重试
            if (e.message.includes('Timeout') && retryCount < maxRetries) {
                console.log(`  ⏳ 等待3秒后重试...`);
                await new Promise(resolve => setTimeout(resolve, 3000));
                // 重试时会创建新页面
                return await this.fetchAuthorWorks(authorId, authorName, retryCount + 1);
            }
            
            return 0;
        }
    }
    
    /**
     * 检查作者笔记是否已在数据库中
     */
    async checkAuthorNotesInDatabase(noteIds) {
        if (!supabase || !noteIds || noteIds.length === 0) {
            return {};
        }
        
        try {
            const { data, error } = await supabase
                .from('xhs_notes')
                .select('id')
                .in('id', noteIds);
            
            if (error) throw error;
            
            const result = {};
            for (const noteId of noteIds) {
                result[noteId] = data.some(item => item.id === noteId);
            }
            return result;
        } catch (e) {
            console.log(`  ⚠️ 检查作者笔记失败: ${e.message}`);
            const result = {};
            for (const noteId of noteIds) {
                result[noteId] = false;
            }
            return result;
        }
    }
    
    /**
     * 更新作者笔记到数据库
     */
    async updateAuthorNoteToDatabase(note, authorId, authorName) {
        if (!supabase) return;
        
        try {
            // 检查笔记是否已存在
            const { data: existing } = await supabase
                .from('xhs_notes')
                .select('*')
                .eq('id', note.id)
                .single();
            
            if (!existing) {
                // 新笔记，插入数据
                const noteData = {
                    id: note.id,
                    title: note.title,
                    note_link: note.link,
                    author_id: authorId,
                    author_name: authorName,
                    author_link: `https://www.xiaohongshu.com/user/profile/${authorId}`,
                    created_at: new Date().toISOString(),
                    hulu_likes: false,
                    hulu_collect: false
                };
                
                await supabase.from('xhs_notes').insert(noteData);
                console.log(`    ✅ 新增作者笔记: ${note.title.substring(0, 30)}...`);
            } else {
                // 已存在，只更新必要字段
                const updateData = {
                    title: note.title || existing.title,
                    note_link: note.link || existing.note_link,
                    author_id: authorId,
                    author_name: authorName,
                    author_link: `https://www.xiaohongshu.com/user/profile/${authorId}`,
                    last_update_time: new Date().toISOString()
                };
                
                // 保留用户标记字段
                for (const key of ['hulu_likes', 'hulu_collect', '500_likes', '500_collect']) {
                    if (key in existing) {
                        updateData[key] = existing[key];
                    }
                }
                
                // 保留我们不会获取到的字段
                for (const key of ['file_id', 'short_link', 'note_type', 'topic', 'location', 'process_failed', 'timestamp', 'content', 'likes_count', 'comments_count', 'collect_count', 'share_count']) {
                    if (key in existing && existing[key] !== null) {
                        updateData[key] = existing[key];
                    }
                }
                
                await supabase.from('xhs_notes').upsert(updateData);
            }
        } catch (e) {
            // 忽略错误
        }
    }
    
    /**
     * 从HTML提取预加载数据
     */
    async extractHtmlNotes(page, dataType) {
        try {
            const htmlContent = await page.content();
            
            // 查找__INITIAL_STATE__
            const stateMatch = htmlContent.match(/window\.__INITIAL_STATE__\s*=\s*({.*?})\s*<\/script>/s);
            if (!stateMatch) {
                console.log("  ⚠️ 未找到预加载数据 - 需要重启浏览器");
                // 不保存HTML，而是抛出错误以触发浏览器重启
                throw new Error('NO_PRELOAD_DATA');
            }
            
            // 解析JSON，处理undefined
            const stateJson = stateMatch[1].replace(/undefined/g, 'null');
            const initialState = JSON.parse(stateJson);
            
            // 调试：打印数据结构
            if (dataType === 'likes') {
                console.log("  🔍 调试点赞页面数据结构:");
                if (initialState.user) {
                    console.log(`    - user对象存在`);
                    if (initialState.user.notes) {
                        console.log(`    - user.notes存在，是数组: ${Array.isArray(initialState.user.notes)}`);
                        if (Array.isArray(initialState.user.notes)) {
                            console.log(`    - user.notes长度: ${initialState.user.notes.length}`);
                            for (let i = 0; i < Math.min(3, initialState.user.notes.length); i++) {
                                const item = initialState.user.notes[i];
                                if (Array.isArray(item)) {
                                    console.log(`    - user.notes[${i}]是数组，长度: ${item.length}`);
                                } else if (item && typeof item === 'object') {
                                    console.log(`    - user.notes[${i}]是对象，有id: ${!!item.id}`);
                                }
                            }
                        }
                    }
                    // 尝试其他可能的路径
                    if (initialState.user.liked) {
                        console.log(`    - user.liked存在`);
                    }
                    if (initialState.user.tabs) {
                        console.log(`    - user.tabs存在`);
                    }
                }
            }
            
            // 根据页面类型提取数据
            let notes = [];
            if (dataType === 'favorites' && initialState.user && initialState.user.notes) {
                // 收藏页面：索引1是收藏数据
                notes = initialState.user.notes[1] || [];
            } else if (dataType === 'likes') {
                // 点赞页面：尝试多个可能的路径
                if (initialState.user) {
                    if (initialState.user.notes && Array.isArray(initialState.user.notes)) {
                        // 尝试索引2（点赞可能在索引2）
                        if (initialState.user.notes[2]) {
                            notes = initialState.user.notes[2];
                            console.log(`    - 使用user.notes[2]作为点赞数据`);
                        } else if (initialState.user.notes[0]) {
                            notes = initialState.user.notes[0];
                            console.log(`    - 使用user.notes[0]作为点赞数据`);
                        }
                    } else if (initialState.user.liked) {
                        notes = initialState.user.liked;
                        console.log(`    - 使用user.liked作为点赞数据`);
                    }
                }
            }
            
            const htmlNotes = {};
            for (const noteWrapper of notes) {
                if (noteWrapper && noteWrapper.id) {
                    const noteCard = noteWrapper.noteCard || {};
                    htmlNotes[noteWrapper.id] = {
                        note_id: noteWrapper.id,
                        display_title: noteCard.displayTitle || '',
                        xsec_token: noteCard.xsecToken || '',
                        // xsec_source 通常为空
                        ...noteCard
                    };
                }
            }
            
            if (Object.keys(htmlNotes).length > 0) {
                console.log(`  📄 HTML预加载: ${Object.keys(htmlNotes).length} 条`);
                
                // 详细打印所有HTML预加载作品信息
                console.log(`  📝 HTML预加载作品详情（全部）:`);
                let count = 0;
                for (const [noteId, note] of Object.entries(htmlNotes)) {
                    const title = note.display_title || note.displayTitle || '无标题';
                    const link = `https://www.xiaohongshu.com/explore/${noteId}${note.xsec_token ? `?xsec_token=${note.xsec_token}` : ''}`;
                    console.log(`    ${count + 1}. 标题: ${title}`);
                    console.log(`       链接: ${link}`);
                    count++;
                }
            }
            
            return htmlNotes;
        } catch (e) {
            console.log(`  ⚠️ 提取HTML数据失败: ${e.message}`);
            // 如果是需要重启浏览器的错误，向上抛出
            if (e.message === 'NO_PRELOAD_DATA') {
                throw e;
            }
            return {};
        }
    }
    
    /**
     * 获取点赞或收藏数据
     */
    async fetchData(dataType = 'likes') {
        const config = {
            likes: {
                path: '/api/sns/web/v1/note/like/page',
                tab: 'liked'
            },
            favorites: {
                path: '/api/sns/web/v2/note/collect/page', 
                tab: 'fav'
            }
        };
        
        if (!config[dataType]) {
            throw new Error(`不支持的类型: ${dataType}`);
        }
        
        const typeConfig = config[dataType];
        const typeName = dataType === 'likes' ? '点赞' : '收藏';
        
        console.log(`\n📊 获取 ${this.userName} 的${typeName}作品`);
        
        // 为每个任务创建新页面
        const page = await this.browserManager.createPage(this.userName);
        console.log(`  📄 为${typeName}任务创建新标签页`);
        
        // 清空之前的数据
        this.apiNotes = {};
        this.apiCount = 0;
        
        let hasMore = true;
        let shouldStop = false;
        let totalNewNotes = 0;
        
        // 设置响应监听器
        const handleResponse = async (response) => {
            const url = response.url();
            
            if (url.includes(typeConfig.path)) {
                this.apiCount++;
                console.log(`  📄 第 ${this.apiCount} 页`);
                
                try {
                    if (response.status() === 200) {
                        const data = await response.json();
                        const notes = (data.data && data.data.notes) || [];
                        hasMore = (data.data && data.data.has_more) || false;
                        
                        console.log(`    获取到: ${notes.length} 条作品`);
                        
                        if (notes.length > 0) {
                            const pageNotes = {};
                            const pageNoteIds = [];
                            
                            // 如果是第一页API调用，详细打印所有作品信息
                            if (this.apiCount === 1) {
                                console.log(`  📝 API第一页作品详情（全部${notes.length}条）:`);
                                for (let i = 0; i < notes.length; i++) {
                                    const note = notes[i];
                                    const title = note.display_title || note.title || '无标题';
                                    const noteId = note.note_id;
                                    const xsecToken = note.xsec_token || '';
                                    const xsecSource = note.xsec_source || '';
                                    let link = `https://www.xiaohongshu.com/explore/${noteId}`;
                                    if (xsecToken && xsecSource) {
                                        link += `?xsec_token=${xsecToken}&xsec_source=${xsecSource}`;
                                    } else if (xsecToken) {
                                        link += `?xsec_token=${xsecToken}`;
                                    }
                                    console.log(`    ${i + 1}. 标题: ${title}`);
                                    console.log(`       链接: ${link}`);
                                }
                                console.log(`  📊 第一页共${notes.length}条作品已全部打印`);
                            }
                            
                            for (const note of notes) {
                                const noteId = note.note_id;
                                if (noteId) {
                                    this.apiNotes[noteId] = note;
                                    pageNotes[noteId] = note;
                                    pageNoteIds.push(noteId);
                                }
                            }
                            
                            // 检查数据库
                            const dbCheck = await this.checkNotesInDatabase(pageNoteIds, dataType);
                            const newNotes = {};
                            const existingNotes = {};
                            const needUpdateNotes = {};  // 需要更新签名的笔记
                            
                            for (const [nid, note] of Object.entries(pageNotes)) {
                                const checkResult = dbCheck[nid];
                                if (!checkResult.exists) {
                                    newNotes[nid] = note;
                                } else if (!checkResult.hasSignature) {
                                    // 存在但没有签名，需要更新
                                    needUpdateNotes[nid] = note;
                                } else {
                                    existingNotes[nid] = note;
                                }
                            }
                            
                            const existingWithSignatureCount = Object.keys(existingNotes).length;
                            const needUpdateCount = Object.keys(needUpdateNotes).length;
                            
                            console.log(`    完整已存在: ${existingWithSignatureCount} 条, 需更新签名: ${needUpdateCount} 条, 新增: ${Object.keys(newNotes).length} 条`);
                            
                            // 更新数据库 - 只更新需要更新的数据
                            const notesToUpdate = {...newNotes, ...needUpdateNotes};
                            if (Object.keys(notesToUpdate).length > 0) {
                                if (Object.keys(newNotes).length > 0) {
                                    totalNewNotes += Object.keys(newNotes).length;
                                }
                                
                                // 完整打印所有要更新的记录
                                console.log(`    📝 准备更新数据库: ${Object.keys(notesToUpdate).length} 条`);
                                console.log(`    📋 更新记录详情:`);
                                let updateIndex = 0;
                                for (const [noteId, note] of Object.entries(notesToUpdate)) {
                                    updateIndex++;
                                    const title = note.display_title || note.title || '无标题';
                                    const xsecToken = note.xsec_token || '';
                                    const xsecSource = note.xsec_source || '';
                                    let link = `https://www.xiaohongshu.com/explore/${noteId}`;
                                    if (xsecToken && xsecSource) {
                                        link += `?xsec_token=${xsecToken}&xsec_source=${xsecSource}`;
                                    } else if (xsecToken) {
                                        link += `?xsec_token=${xsecToken}`;
                                    }
                                    
                                    const updateType = newNotes[noteId] ? '新增' : '更新签名';
                                    console.log(`      ${updateIndex}. [${updateType}] ${title}`);
                                    console.log(`         ID: ${noteId}`);
                                    console.log(`         链接: ${link}`);
                                }
                                
                                await this.updateDatabase(notesToUpdate, dataType);
                                console.log(`    ✅ 数据库更新完成`);
                            } else if (this.isFullMode) {
                                console.log(`    ✅ 全量模式 - 本页数据都已是最新，无需更新`);
                            }
                            
                            // 增量模式：当全部已存在且都有完整签名时才停止
                            if (!this.isFullMode && existingWithSignatureCount === pageNoteIds.length && pageNoteIds.length > 0) {
                                console.log(`  ⏹️ 所有作品都已存在且有完整签名，停止获取`);
                                shouldStop = true;
                                hasMore = false;
                            } 
                            // 全量模式：只获取第一页
                            else if (this.isFullMode) {
                                if (this.apiCount >= 1) {
                                    console.log(`  ⏹️ 全量模式 - 已获取第一页数据，停止获取更多页`);
                                    shouldStop = true;
                                    hasMore = false;
                                } else {
                                    console.log(`  ℹ️ 全量模式 - 等待获取第一页API数据`);
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.log(`  ⚠️ 解析失败: ${e.message}`);
                }
            }
        };
        
        // 添加监听器
        page.on('response', handleResponse);
        
        try {
            // 导航到目标页面
            const targetUrl = `https://www.xiaohongshu.com/user/profile/${this.userId}?tab=${typeConfig.tab}`;
            console.log(`  🔄 导航到${typeName}页面...`);
            await page.goto(targetUrl, { waitUntil: 'domcontentloaded', timeout: 15000 });
            
            await page.waitForTimeout(3000);
            
            // 提取HTML预加载数据（前10-14条）
            let htmlNotes = {};
            try {
                htmlNotes = await this.extractHtmlNotes(page, dataType);
            } catch (extractError) {
                if (extractError.message === 'NO_PRELOAD_DATA') {
                    // 关闭当前页面
                    try {
                        await page.close();
                        console.log(`  📄 关闭当前页面，准备重启浏览器`);
                    } catch (closeError) {
                        // 忽略关闭错误
                    }
                    
                    // 移除监听器
                    page.removeListener('response', handleResponse);
                    
                    // 抛出特定错误以触发浏览器重启
                    throw new Error('BROWSER_RESTART_NEEDED');
                }
                throw extractError;
            }
            
            // 如果是点赞页面且没有获取到数据，可能需要登录
            if (dataType === 'likes' && Object.keys(htmlNotes).length === 0 && this.apiCount === 0) {
                console.log(`  ⚠️ 未获取到点赞数据，检查登录状态...`);
                
                // 检查并处理登录
                const loginSuccess = await this.checkAndHandleLoginForFetcher(page);
                
                if (loginSuccess) {
                    console.log(`  🔄 登录成功，重新获取点赞数据...`);
                    // 重新导航到点赞页面
                    await page.goto(targetUrl, { waitUntil: 'domcontentloaded', timeout: 15000 });
                    await page.waitForTimeout(3000);
                    
                    // 重新提取数据
                    try {
                        const newHtmlNotes = await this.extractHtmlNotes(page, dataType);
                        Object.assign(htmlNotes, newHtmlNotes);
                    } catch (extractError) {
                        if (extractError.message === 'NO_PRELOAD_DATA') {
                            // 关闭当前页面
                            try {
                                await page.close();
                                console.log(`  📄 关闭当前页面，准备重启浏览器`);
                            } catch (closeError) {
                                // 忽略关闭错误
                            }
                            
                            // 移除监听器
                            page.removeListener('response', handleResponse);
                            
                            // 抛出特定错误以触发浏览器重启
                            throw new Error('BROWSER_RESTART_NEEDED');
                        }
                        throw extractError;
                    }
                } else {
                    console.log(`  ❌ 登录失败，无法获取点赞数据`);
                    throw new Error('需要登录才能获取点赞数据');
                }
            }
            
            // 将HTML数据加入到总数据中
            for (const [noteId, noteData] of Object.entries(htmlNotes)) {
                if (!this.apiNotes[noteId]) {
                    this.apiNotes[noteId] = noteData;
                }
            }
            
            // 检查HTML数据是否已在数据库中
            if (Object.keys(htmlNotes).length > 0) {
                const htmlNoteIds = Object.keys(htmlNotes);
                const dbCheck = await this.checkNotesInDatabase(htmlNoteIds, dataType);
                
                for (const [noteId, noteData] of Object.entries(htmlNotes)) {
                    const checkResult = dbCheck[noteId];
                    // 如果不存在或没有签名，都需要更新
                    if (!checkResult.exists || !checkResult.hasSignature) {
                        // 创建一个临时对象来调用updateDatabase
                        const tempNotes = { [noteId]: noteData };
                        await this.updateDatabase(tempNotes, dataType);
                        if (!checkResult.exists) {
                            totalNewNotes++;
                        }
                    }
                }
            }
            
            // 滚动获取数据
            let scrollCount = 0;
            while (hasMore && !shouldStop) {
                scrollCount++;
                await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
                await page.waitForTimeout(Math.random() * 1000 + 2000);
                
                // 每10次滚动输出一次进度
                if (scrollCount % 10 === 0) {
                    console.log(`  📜 已滚动 ${scrollCount} 次，继续获取...`);
                }
            }
            
            console.log(`  ✅ ${typeName}数据获取完成: 总计 ${Object.keys(this.apiNotes).length} 条, 新增 ${totalNewNotes} 条`);
            
        } finally {
            // 移除监听器
            page.removeListener('response', handleResponse);
            
            // 关闭页面
            try {
                await page.close();
                console.log(`  📄 关闭${typeName}任务的标签页`);
            } catch (closeError) {
                // 忽略关闭错误
            }
        }
        
        return totalNewNotes > 0;
    }
    
    /**
     * 检查笔记是否已在数据库中（含链接完整性检查）
     */
    async checkNotesInDatabase(noteIds, dataType) {
        if (!supabase || !noteIds || noteIds.length === 0) {
            const result = {};
            for (const noteId of noteIds) {
                result[noteId] = { exists: false, hasSignature: false };
            }
            return result;
        }
        
        try {
            const fieldName = `${this.userName}_${dataType.replace('favorites', 'collect')}`;
            
            const { data, error } = await supabase
                .from('xhs_notes')
                .select(`id, ${fieldName}, note_link`)
                .in('id', noteIds);
            
            if (error) throw error;
            
            const result = {};
            for (const noteId of noteIds) {
                const record = data.find(item => item.id === noteId);
                if (record && record[fieldName] === true) {
                    // 检查链接是否包含签名参数
                    const hasSignature = record.note_link && 
                                       (record.note_link.includes('xsec_token=') || 
                                        record.note_link.includes('xsec_source='));
                    result[noteId] = { exists: true, hasSignature: hasSignature };
                } else {
                    result[noteId] = { exists: false, hasSignature: false };
                }
            }
            return result;
            
        } catch (e) {
            console.log(`❌ 数据库查询失败: ${e}`);
            const result = {};
            for (const noteId of noteIds) {
                result[noteId] = { exists: false, hasSignature: false };
            }
            return result;
        }
    }
    
    /**
     * 更新数据库
     */
    async updateDatabase(notesData, dataType) {
        if (!supabase || !notesData || Object.keys(notesData).length === 0) {
            return;
        }
        
        try {
            const fieldName = `${this.userName}_${dataType.replace('favorites', 'collect')}`;
            
            for (const [noteId, noteInfo] of Object.entries(notesData)) {
                try {
                    // 查询现有记录
                    const { data: existing } = await supabase
                        .from('xhs_notes')
                        .select('*')
                        .eq('id', noteId);
                    
                    let updateData = {
                        id: noteId,
                        [fieldName]: true,
                        last_update_time: new Date().toISOString()
                    };
                    
                    if (existing && existing.length > 0) {
                        const existingRecord = existing[0];
                        
                        // 保留其他用户的标记
                        for (const key of ['500_likes', '500_collect', 'hulu_likes', 'hulu_collect']) {
                            if (key !== fieldName && key in existingRecord) {
                                updateData[key] = existingRecord[key];
                            }
                        }
                        
                        // 保留我们不会获取到的字段（如file_id等）
                        for (const key of ['file_id', 'short_link', 'note_type', 'topic', 'location', 'process_failed', 'timestamp', 'content']) {
                            if (key in existingRecord && existingRecord[key] !== null) {
                                updateData[key] = existingRecord[key];
                            }
                        }
                        
                        // 更新基本信息
                        updateData.title = noteInfo.display_title || noteInfo.title || existingRecord.title || '';
                        
                        // 更新作者信息
                        if (noteInfo.user) {
                            updateData.author_id = noteInfo.user.user_id || '';
                            updateData.author_name = noteInfo.user.nickname || '';
                            if (noteInfo.user.user_id) {
                                updateData.author_link = `https://www.xiaohongshu.com/user/profile/${noteInfo.user.user_id}`;
                            }
                        }
                        
                        // 更新交互数据
                        if (noteInfo.interact_info) {
                            updateData.likes_count = noteInfo.interact_info.liked_count || '';
                            updateData.comments_count = noteInfo.interact_info.comment_count || '';
                            updateData.collect_count = noteInfo.interact_info.collected_count || '';
                            updateData.share_count = noteInfo.interact_info.share_count || '';
                        }
                    } else {
                        // 新记录
                        updateData = {
                            ...updateData,
                            title: noteInfo.display_title || noteInfo.title || '',
                            '500_likes': false,
                            '500_collect': false,
                            'hulu_likes': false,
                            'hulu_collect': false
                        };
                        
                        updateData[fieldName] = true;
                        
                        if (noteInfo.user) {
                            updateData.author_id = noteInfo.user.user_id || '';
                            updateData.author_name = noteInfo.user.nickname || '';
                            if (noteInfo.user.user_id) {
                                updateData.author_link = `https://www.xiaohongshu.com/user/profile/${noteInfo.user.user_id}`;
                            }
                        }
                        
                        if (noteInfo.interact_info) {
                            updateData.likes_count = noteInfo.interact_info.liked_count || '';
                            updateData.comments_count = noteInfo.interact_info.comment_count || '';
                            updateData.collect_count = noteInfo.interact_info.collected_count || '';
                            updateData.share_count = noteInfo.interact_info.share_count || '';
                        }
                    }
                    
                    // 生成带签名参数的完整链接
                    const xsecToken = noteInfo.xsec_token || '';
                    const xsecSource = noteInfo.xsec_source || '';
                    
                    if (xsecToken && xsecSource) {
                        updateData.note_link = `https://www.xiaohongshu.com/explore/${noteId}?xsec_token=${xsecToken}&xsec_source=${xsecSource}`;
                    } else if (xsecToken) {
                        updateData.note_link = `https://www.xiaohongshu.com/explore/${noteId}?xsec_token=${xsecToken}`;
                    } else {
                        updateData.note_link = `https://www.xiaohongshu.com/explore/${noteId}`;
                    }
                    
                    await supabase.from('xhs_notes').upsert(updateData);
                    
                } catch (e) {
                    console.log(`  ⚠️ 更新笔记 ${noteId} 失败: ${e.message}`);
                }
            }
            
        } catch (e) {
            console.log(`❌ 数据库更新失败: ${e}`);
        }
    }
}

/**
 * 持久监控模式
 */
async function persistentMonitorMode(isFullMode = false) {
    let isExiting = false;  // 退出标志
    let realtimeChannel = null;  // Realtime订阅频道
    
    // 用户配置
    const USERS = {
        'hulu': {
            user_id: '60b1967c000000000101fd47',
            nickname: 'hulu'
        }
    };
    
    console.log("🚀 小红书持久监控模式启动 v3.8.0");
    console.log("=".repeat(60));
    console.log("监控账号: hulu");
    console.log("监控内容: 点赞、收藏和订阅作者作品");
    console.log("监控策略: 浏览器持久运行，监听数据库触发");
    console.log("触发条件: does.douyinlikes字段变化");
    console.log("=".repeat(60));
    
    // 创建浏览器管理器
    const browserManager = new BrowserManager();
    
    // 注册优雅退出处理
    const gracefulShutdown = async (signal) => {
        if (isExiting) return; // 防止重复调用
        
        console.log(`\n\n📛 收到 ${signal} 信号，正在优雅退出...`);
        isExiting = true;  // 设置退出标志
        browserManager.isShuttingDown = true; // 立即设置浏览器关闭标志
        
        // 清理Realtime订阅
        if (realtimeChannel) {
            console.log("🔌 正在断开Realtime订阅...");
            await supabase.removeChannel(realtimeChannel);
            realtimeChannel = null;
        }
        
        // 等待一秒让当前操作停止
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 关闭所有浏览器
        await browserManager.closeAll();
        
        // 强制清理所有chromium进程
        try {
            const { execSync } = await import('child_process');
            try {
                execSync('pkill -9 -f chromium', { 
                    encoding: 'utf-8',
                    stdio: 'ignore',
                    shell: true
                });
            } catch (e) {
                // pkill返回非0是正常的，忽略
            }
            console.log("✅ 已清理所有浏览器进程");
        } catch (e) {
            // 忽略错误
        }
        
        process.exit(0);
    };
    
    // 使用异步处理器
    process.on('SIGINT', async () => await gracefulShutdown('SIGINT'));
    process.on('SIGTERM', async () => await gracefulShutdown('SIGTERM'));
    
    // 创建获取器实例
    const fetchers = {};
    
    try {
        // 初始化所有浏览器
        await browserManager.initialize(USERS);
        
        // 创建获取器
        for (const [userName, account] of Object.entries(USERS)) {
            fetchers[userName] = new PersistentFetcher(
                browserManager,
                userName,
                account.user_id,
                isFullMode  // 传递全量模式参数
            );
        }
        
        // 设置Realtime监听
        let triggerReceived = false;
        let waitingForTrigger = false;
        
        if (supabase) {
            console.log("\n🔌 设置Realtime监听...");
            
            // 创建Realtime订阅
            realtimeChannel = supabase
                .channel('does-changes')
                .on(
                    'postgres_changes',
                    {
                        event: 'UPDATE',
                        schema: 'public',
                        table: 'does',
                        filter: 'id=eq.1'
                    },
                    (payload) => {
                        // 检查douyinlikes字段是否真正发生变化
                        const oldValue = payload.old?.douyinlikes;
                        const newValue = payload.new?.douyinlikes;
                        
                        // 只有值真正变化时才处理
                        if (oldValue !== newValue) {
                            console.log('\n🔔 检测到数据库变化!');
                            console.log(`  📊 douyinlikes字段变化: ${oldValue} -> ${newValue}`);
                            
                            // 只有在等待触发时才响应
                            if (waitingForTrigger) {
                                triggerReceived = true;
                                console.log('  ✅ 触发新一轮监控循环');
                            } else {
                                console.log('  ℹ️ 当前正在执行任务，忽略此次触发');
                            }
                        } else {
                            // 值没有变化，可能是其他字段更新
                            // 不输出日志，避免干扰
                        }
                    }
                )
                .subscribe((status) => {
                    if (status === 'SUBSCRIBED') {
                        console.log('✅ Realtime监听已建立');
                        console.log('📍 监听表: does (id=1)');
                        console.log('📍 监听字段: douyinlikes');
                    } else if (status === 'CHANNEL_ERROR') {
                        console.log('❌ Realtime连接错误');
                    } else if (status === 'TIMED_OUT') {
                        console.log('⚠️ Realtime连接超时');
                    } else if (status === 'CLOSED') {
                        console.log('🔌 Realtime连接已关闭');
                    }
                });
            
            // 等待订阅建立
            await new Promise(resolve => setTimeout(resolve, 2000));
        } else {
            console.log("\n⚠️ Supabase未连接，将使用定时循环模式");
        }
        
        console.log("\n✅ 初始化完成，开始监控循环");
        console.log("💡 提示: 按 Ctrl+C 停止程序");
        console.log("=".repeat(60));
        
        let cycleCount = 0;
        
        // 监控循环
        while (!isExiting) {
            cycleCount++;
            console.log(`\n${"=".repeat(80)}`);
            console.log(`🔄 第 ${cycleCount} 轮监控`);
            console.log(`时间: ${new Date().toLocaleString('zh-CN')}`);
            console.log(`${"=".repeat(80)}`);
            
            // 检查是否正在退出
            if (isExiting || browserManager.isShuttingDown) {
                console.log("⚠️ 检测到退出信号，停止监控");
                break;
            }
            
            // 检查浏览器健康状态
            for (const userName of Object.keys(USERS)) {
                const isHealthy = await browserManager.checkBrowserHealth(userName);
                if (!isHealthy) {
                    console.log(`\n⚠️ ${userName} 浏览器异常，正在重启...`);
                    await browserManager.restartBrowser(userName, USERS[userName]);
                    // 重新创建fetcher
                    fetchers[userName] = new PersistentFetcher(
                        browserManager,
                        userName,
                        USERS[userName].user_id,
                        isFullMode  // 传递全量模式参数
                    );
                }
            }
            
            // 任务列表
            const tasks = [
                ['hulu', 'likes'],
                ['hulu', 'favorites'],
                ['hulu', 'authors']  // 添加作者监控任务
            ];
            
            let totalNewInCycle = 0;
            
            for (const [userName, dataType] of tasks) {
                // 检查是否正在退出
                if (isExiting) {
                    console.log("⚠️ 检测到退出信号，停止任务执行");
                    break;
                }
                
                if (dataType === 'authors') {
                    // 处理订阅作者监控
                    console.log(`\n📍 任务: ${userName} - 订阅作者作品监控`);
                    
                    try {
                        const authors = await fetchers[userName].getSubscribedAuthors();
                        let authorNewCount = 0;
                        
                        for (const author of authors) {
                            // 检查是否正在退出
                            if (isExiting || browserManager.isShuttingDown) {
                                console.log("⚠️ 检测到退出信号，停止处理作者");
                                break;
                            }
                            
                            console.log(`\n  👤 检查作者: ${author.nickname}`);
                            try {
                                const newNotes = await fetchers[userName].fetchAuthorWorks(
                                    author.userid, 
                                    author.nickname
                                );
                                if (newNotes > 0) {
                                    authorNewCount++;
                                }
                            } catch (authorError) {
                                if (authorError.message === 'BROWSER_RESTART_NEEDED') {
                                    console.log(`\n🔄 检测到需要重启浏览器，正在重启 ${userName} 的浏览器...`);
                                    await browserManager.restartBrowser(userName, USERS[userName]);
                                    // 重新创建fetcher
                                    fetchers[userName] = new PersistentFetcher(
                                        browserManager,
                                        userName,
                                        USERS[userName].user_id,
                                        isFullMode
                                    );
                                    console.log(`✅ 浏览器已重启，重试获取作者 ${author.nickname} 的作品...`);
                                    
                                    // 重试当前作者
                                    try {
                                        const newNotes = await fetchers[userName].fetchAuthorWorks(
                                            author.userid, 
                                            author.nickname
                                        );
                                        if (newNotes > 0) {
                                            authorNewCount++;
                                        }
                                    } catch (retryError) {
                                        console.log(`  ⚠️ 重试失败: ${retryError.message}`);
                                    }
                                } else if (authorError.message.includes('浏览器正在关闭') || 
                                    authorError.message.includes('浏览器已关闭')) {
                                    console.log(`  ⚠️ 浏览器已关闭，停止处理`);
                                    break;
                                } else {
                                    console.log(`  ⚠️ 处理作者 ${author.nickname} 时出错: ${authorError.message}`);
                                    console.log(`  ➡️ 继续处理下一个作者...`);
                                }
                            }
                        }
                        
                        if (authorNewCount > 0) {
                            totalNewInCycle++;
                            console.log(`✅ ${authorNewCount} 个作者有新作品`);
                        } else {
                            console.log(`ℹ️ 订阅作者无新作品`);
                        }
                        
                    } catch (e) {
                        console.log(`❌ 作者监控失败: ${e.message}`);
                    }
                    
                } else {
                    // 处理点赞和收藏
                    const typeName = dataType === 'likes' ? '点赞' : '收藏';
                    
                    console.log(`\n📍 任务: ${userName} - ${typeName}`);
                    
                    try {
                        const hasNewData = await fetchers[userName].fetchData(dataType);
                        
                        if (hasNewData) {
                            totalNewInCycle++;
                            console.log(`✅ 获取到新数据`);
                        } else {
                            console.log(`ℹ️ 无新数据`);
                        }
                        
                    } catch (e) {
                        if (e.message === 'BROWSER_RESTART_NEEDED') {
                            console.log(`\n🔄 检测到需要重启浏览器，正在重启 ${userName} 的浏览器...`);
                            await browserManager.restartBrowser(userName, USERS[userName]);
                            // 重新创建fetcher
                            fetchers[userName] = new PersistentFetcher(
                                browserManager,
                                userName,
                                USERS[userName].user_id,
                                isFullMode
                            );
                            console.log(`✅ 浏览器已重启，重试任务...`);
                            
                            // 重试任务
                            try {
                                const hasNewData = await fetchers[userName].fetchData(dataType);
                                if (hasNewData) {
                                    totalNewInCycle++;
                                    console.log(`✅ 获取到新数据`);
                                } else {
                                    console.log(`ℹ️ 无新数据`);
                                }
                            } catch (retryError) {
                                console.log(`❌ 重试失败: ${retryError.message}`);
                            }
                        } else {
                            console.log(`❌ 处理失败: ${e.message}`);
                        }
                    }
                }
            }
            
            // 统计
            console.log(`\n${"=".repeat(80)}`);
            console.log(`📊 第 ${cycleCount} 轮完成`);
            console.log(`本轮新数据任务数: ${totalNewInCycle}`);
            console.log(`${"=".repeat(80)}`);
            
            // 保存统计信息到数据库
            if (supabase) {
                try {
                    // 构建日志信息
                    const logEntry = `[${new Date().toLocaleString('zh-CN')}] 第${cycleCount}轮 - 新数据任务数: ${totalNewInCycle}\n`;
                    
                    // 先获取现有的log2内容
                    const { data: existingData, error: fetchError } = await supabase
                        .from('does')
                        .select('log2')
                        .eq('id', 1)
                        .single();
                    
                    if (!fetchError) {
                        // 追加到现有log2（如果存在）
                        const currentLog = existingData?.log2 || '';
                        const updatedLog = currentLog + logEntry;
                        
                        // 限制log长度（保留最近的10000个字符）
                        const truncatedLog = updatedLog.length > 10000 
                            ? '...' + updatedLog.slice(-9997) 
                            : updatedLog;
                        
                        // 更新数据库
                        const { error: updateError } = await supabase
                            .from('does')
                            .update({ log2: truncatedLog })
                            .eq('id', 1);
                        
                        if (!updateError) {
                            console.log(`  📝 统计信息已保存到数据库 (log2字段)`);
                        } else {
                            console.log(`  ⚠️ 保存统计信息失败: ${updateError.message}`);
                        }
                    } else {
                        // 如果记录不存在，尝试插入
                        const { error: insertError } = await supabase
                            .from('does')
                            .insert({ id: 1, log2: logEntry, clean: false, scanall: false, all: false });
                        
                        if (!insertError) {
                            console.log(`  📝 统计信息已保存到数据库（新建记录，log2字段）`);
                        } else {
                            console.log(`  ⚠️ 保存统计信息失败: ${insertError.message}`);
                        }
                    }
                } catch (e) {
                    console.log(`  ⚠️ 保存统计信息时出错: ${e.message}`);
                }
            }
            
            // 等待触发信号
            if (supabase && realtimeChannel) {
                console.log(`\n⏳ 等待数据库触发信号...`);
                console.log(`📍 监听: does.douyinlikes 字段变化`);
                console.log(`💡 提示: 修改 does 表的 douyinlikes 字段来触发新一轮监控`);
                
                // 设置等待标志
                waitingForTrigger = true;
                triggerReceived = false;
                
                // 等待触发或退出信号
                while (!triggerReceived && !isExiting && !browserManager.isShuttingDown) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                // 重置等待标志
                waitingForTrigger = false;
                
                if (triggerReceived) {
                    console.log(`\n🎯 收到触发信号，开始下一轮监控...`);
                    // 短暂延迟，避免过快响应
                    await new Promise(resolve => setTimeout(resolve, 2000));
                } else if (isExiting || browserManager.isShuttingDown) {
                    console.log("\n⚠️ 检测到退出信号，停止等待");
                    break;
                }
            } else {
                // 没有Realtime连接时，使用原来的定时等待
                const waitSeconds = Math.floor(Math.random() * 31) + 30;
                console.log(`\n💤 休息 ${waitSeconds} 秒后开始下一轮...`);
                console.log(`下次时间: ${new Date(Date.now() + waitSeconds * 1000).toLocaleString('zh-CN')}`);
                
                // 分段等待，以便及时响应退出信号
                for (let i = 0; i < waitSeconds; i++) {
                    if (isExiting || browserManager.isShuttingDown) {
                        console.log("\n⚠️ 检测到退出信号，停止等待");
                        break;
                    }
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
        }
        
    } catch (e) {
        console.error(`\n❌ 程序异常: ${e}`);
        console.error(e);
    } finally {
        // 清理Realtime订阅
        if (realtimeChannel) {
            console.log("\n🔌 清理Realtime订阅...");
            await supabase.removeChannel(realtimeChannel);
            realtimeChannel = null;
        }
        
        // 清理：关闭所有浏览器
        console.log("\n🔚 程序退出，正在清理...");
        await browserManager.closeAll();
        
        // 再次强制清理所有chromium进程
        try {
            const { execSync } = await import('child_process');
            try {
                execSync('pkill -9 -f chromium', { 
                    encoding: 'utf-8',
                    stdio: 'ignore',
                    shell: true
                });
            } catch (e) {
                // pkill返回非0是正常的，忽略
            }
            console.log("✅ 已清理所有浏览器进程");
        } catch (e) {
            // 忽略错误
        }
        
        console.log("✅ 程序退出完成");
    }
}

/**
 * 主函数
 */
async function main() {
    const program = new Command();
    
    program
        .name('xhs')
        .description('小红书数据获取工具 - Realtime触发版本')
        .version('3.9.0')
        .argument('[mode]', '运行模式: monitor(默认) 或 all(全量模式)', 'monitor')
        .parse(process.argv);
    
    const mode = program.args[0] || 'monitor';
    
    if (mode === 'monitor') {
        await persistentMonitorMode(false);  // 增量模式
    } else if (mode === 'all') {
        console.log("🚀 启动全量模式 - 点赞/收藏只获取第一页，作者作品获取全部");
        await persistentMonitorMode(true);   // 全量模式
    } else {
        console.log(`不支持的模式: ${mode}`);
        console.log("使用方法:");
        console.log("  node xhs.js         - 增量监控模式（默认）");
        console.log("  node xhs.js monitor - 增量监控模式");
        console.log("  node xhs.js all     - 全量模式（点赞/收藏仅第一页）");
    }
}


// 执行主函数
main().catch(console.error);