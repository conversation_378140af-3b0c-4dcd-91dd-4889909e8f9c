# 小红书API完整文档

## 一、核心API端点

### 1. 用户点赞列表API ⭐️ [已验证]
**端点**: `https://edith.xiaohongshu.com/api/sns/v1/note/liked`  
**方法**: GET  
**参数**: 
- `user_id`: 用户ID (必需) - 格式: 24位hex字符串
- `cursor`: 分页游标 - 用于获取下一页数据
- `num`: 每页数量 - 默认20

**用途**: 获取指定用户点赞的所有笔记列表

### 2. 用户资料API
**端点**: `https://as.xiaohongshu.com/api/v1/profile/android`  
**方法**: POST  
**请求体格式**:
```json
{
  "a": "ECFAAF01",        // App ID
  "c": [操作码],          // 不同操作的代码
  "d": "[加密数据]",      // AES加密的业务数据
  "e": {                  // 环境信息
    "device_id": "...",
    "sid": "session...",
    "uid": "..."
  },
  "g": "[gid]",          // 会话ID
  "k": "[密钥]",         // 加密密钥
  "p": "a",              // 平台 (a=android)
  "s": "[签名]",         // 数据签名
  "v": "2.9.26"          // Shield版本
}
```

### 3. 点赞操作API
**端点**: `https://edith.xiaohongshu.com/api/sns/v1/note/like`  
**方法**: POST  
**参数**: 
- `oid`: 笔记ID
- 其他参数待确认

### 4. 笔记详情预加载
**端点**: `https://edith.xiaohongshu.com/api/sns/v1/note/detailfeed/pre`  
**方法**: GET/POST  
**用途**: 预加载笔记详情数据

### 5. 主页Feed流
**端点**: `https://edith.xiaohongshu.com/api/sns/v1/homefeed/`  
**方法**: GET  
**用途**: 获取主页推荐内容流

## 二、Shield安全机制

### 必需的请求头

#### 1. x-mini-gid
- **说明**: 会话组ID
- **格式**: 64位十六进制字符串
- **示例**: `7ce64074eeaa54dd32f71379dd312e6f3c10960947359cac77c030eb`
- **生成**: 每次会话生成，保持不变

#### 2. x-mini-s1
- **说明**: Base64编码的签名数据
- **格式**: Base64字符串
- **示例**: `ABgAAAABEmuv3M7VNnprJY6Qv23EYGF9fKJoZYnTI+Rr015yJ9lH...`
- **生成**: 对请求数据进行签名后Base64编码

#### 3. x-mini-sig
- **说明**: 请求签名
- **格式**: 64位十六进制字符串（SHA256）
- **示例**: `dcffdd2d7294521cd9f3b3d0bcda46249af1b60dc507ffc2ad1e2c38be1a2d72`
- **生成**: HMAC-SHA256签名

#### 4. x-mini-mua
- **说明**: JWT格式的复杂认证载荷
- **格式**: JWT token (通常1600+字符)
- **结构**: 
```json
{
  "a": "ECFAAF01",     // App ID
  "c": 53,             // 操作码
  "k": "[密钥]",       // 加密密钥
  "p": "a",            // 平台
  "s": "[签名]",       // 内部签名
  "t": {               // 时间戳和计数器
    "c": 3,
    "d": 4,
    "f": 0,
    "s": 4098,
    "t": 14923026,
    "tt": [1]
  },
  "u": "[用户标识]",   // 用户唯一ID
  "v": "2.9.26"        // Shield版本
}
```

#### 5. xy-common-params
- **说明**: URL编码的通用参数
- **包含内容**:
  - `fid`: 设备指纹ID
  - `gid`: 会话组ID
  - `device_model`: 设备型号
  - `tz`: 时区
  - `channel`: 渠道（如GooglePlay）
  - `versionName`: App版本
  - `deviceId`: 设备ID
  - `platform`: 平台
  - `sid`: 会话ID
  - `identifier_flag`: 标识符
  - `project_id`: 项目ID
  - `x_trace_page_current`: 当前页面
  - `lang`: 语言
  - `app_id`: 应用ID
  - `uis`: UI状态
  - `teenager`: 青少年模式
  - `active_ctry`: 活跃国家
  - `cpu_name`: CPU名称
  - `dlang`: 设备语言
  - `data_ctry`: 数据国家
  - `SUE`: 未知参数
  - `launch_id`: 启动ID
  - `origin_channel`: 原始渠道
  - `overseas_channel`: 海外渠道
  - `mlanguage`: 多语言
  - `folder_type`: 文件夹类型
  - `auto_trans`: 自动翻译
  - `t`: 时间戳
  - `build`: 构建版本
  - `holder_ctry`: 持有国家
  - `did`: 设备ID的另一种形式

## 三、其他重要信息

### Shield SDK组件
- `com.xingin.shield.http.a`
- `com.xingin.shield.http.b`
- `com.xingin.shield.http.c`
- `com.xingin.shield.http.d`
- `com.xingin.shield.http.ContextHolder`

### Matrix框架相关类
- `com.xingin.matrix.v2.profile.newpage.noteinfo.likes.UserNoteLikeModel$UserLikedServers`
  - 方法: `loadUserLikedNotes`, `loadUserLikedNotesV1`, `loadUserNiceNotes`
- `com.xingin.matrix.v2.profile.newpage.noteinfo.collect.UserCollectedModel`
- `com.xingin.matrix.v2.profile.newpage.noteinfo.collect.ProfileCollectView`

### 技术栈
- **网络库**: OkHttp3
- **异步处理**: RxJava
- **可能的长连接**: WebSocket
- **UI框架**: Matrix V2 (小红书自研)
- **数据格式**: JSON + Protocol Buffers (二进制部分)

## 四、API调用流程

### 获取点赞列表流程
1. **用户登录**
   - 获取session ID (sid)
   - 获取用户ID (uid)
   - 初始化Shield上下文

2. **生成认证参数**
   - 生成x-mini-gid (会话ID)
   - 计算x-mini-sig (签名)
   - 构建x-mini-mua (JWT)
   - 组装xy-common-params

3. **请求点赞列表**
   ```
   GET https://edith.xiaohongshu.com/api/sns/v1/note/liked?user_id=[用户ID]
   Headers:
     x-mini-gid: [会话ID]
     x-mini-s1: [Base64签名]
     x-mini-sig: [HMAC签名]
     x-mini-mua: [JWT token]
     xy-common-params: [设备参数]
   ```

4. **处理响应**
   - 响应可能是加密的
   - 需要使用相同的密钥解密
   - 解析笔记列表数据

## 五、注意事项

1. **Shield保护严格**
   - 所有参数相互关联
   - 时间戳验证（可能有5-10分钟窗口）
   - 设备指纹绑定
   - 请求顺序可能被验证

2. **数据加密**
   - 请求体的"d"字段是AES加密的
   - 密钥在请求体的"k"字段中
   - 响应数据可能也是加密的

3. **会话管理**
   - sid需要保持一致
   - gid在整个会话期间不变
   - token可能需要定期刷新

4. **设备绑定**
   - device_id必须一致
   - User-Agent需要匹配
   - 设备信息需要真实

## 六、实现建议

要成功调用这些API，需要：

1. **逆向Shield SDK**
   - 分析签名生成算法
   - 理解JWT构造逻辑
   - 破解AES加密方式

2. **模拟完整环境**
   - 保持设备信息一致
   - 维护会话状态
   - 处理时间同步

3. **处理反爬虫**
   - 控制请求频率
   - 模拟真实用户行为
   - 处理验证码等挑战

## 七、已确认的API列表

### 用户相关
- `/api/v1/profile/android` - 用户资料
- `/api/sns/v1/note/liked` - 用户点赞列表 ⭐ [已捕获]
- `/api/sns/v1/user/info` - 用户信息
- `/api/sns/celestial/connect/config` - 连接配置
- `/api/sns/v4/note/user/posted` - 用户发布的笔记
- `/api/sns/v3/user/me` - 当前用户信息
- `/api/sns/v1/user/me/authority` - 用户权限

### 收藏相关 ⭐ [已捕获]
- `/api/sns/v1/note/collection/list` - 收藏列表
- `/api/sns/v1/user/collect_filter` - 收藏筛选器
- `/api/sns/v1/note/faved` - 收藏的笔记

### 笔记相关
- `/api/sns/v1/note/like` - 点赞操作
- `/api/sns/v1/note/detailfeed/pre` - 笔记详情预加载
- `/api/sns/v1/note/detailfeed/preload` - 笔记详情预载
- `/api/sns/v1/note/feed` - 笔记流
- `/api/sns/v1/note/tabfeed` - 标签页笔记流
- `/api/sns/v1/note/imagefeed` - 图片笔记流
- `/api/sns/v5/note/comment/list` - 评论列表

### Feed相关
- `/api/sns/v1/homefeed/` - 主页Feed
- `/api/sns/v6/homefeed` - 主页Feed v6
- `/api/sns/v6/homefeed/categories` - 分类
- `/api/sns/v1/homefeed/client_downgrade/report` - 客户端降级报告

### 关注相关
- `/api/sns/v1/followings/reddot` - 关注红点提示
- `/api/sns/v5/recommend/user/explore` - 推荐用户探索

### 其他
- `/api/sns/v1/tag/ads_engage` - 广告互动
- `/api/sns/v1/attribution/appsflyer` - 归因追踪
- `/api/sns/v1/paddles/pull_configs` - 配置拉取
- `/api/sns/v2/message/config` - 消息配置
- `/api/sns/v6/message/detect` - 消息检测

## 八、API参数详情

### 点赞API (Likes) ❤️
**端点**: `/api/sns/v1/note/liked`  
**方法**: GET  
**参数**:
- `user_id` (必需): 用户ID
- `cursor` (可选): 分页游标
- `num` (可选): 每页数量

### 收藏API (Collections) 📚
**1. 获取收藏列表**  
**端点**: `/api/sns/v1/note/faved`  
**方法**: GET  
**参数**:
- `user_id` (必需): 用户ID
- `cursor` (可选): 分页游标
- `num` (可选): 每页数量

**2. 收藏筛选器**  
**端点**: `/api/sns/v1/user/collect_filter`  
**方法**: GET  
**参数**:
- `user_id` (必需): 用户ID
- `post_later_flag` (可选): 稍后发布标记

### 关注API (Following) 👥 [已捕获]
**1. 获取关注列表**  
**端点**: `/api/sns/v2/user/followings/self`  
**方法**: GET  
**参数**:
- `cursor` (可选): 分页游标
- `order` (可选): 排序方式
- `tab_type` (可选): 标签类型

**2. 获取关注额外信息**  
**端点**: `/api/sns/v2/user/followings/extra`  
**方法**: GET  
**参数**:
- `user_id_list` (必需): 用户ID列表
- `tab_type` (可选): 标签类型

**3. 所有关注用户**  
**端点**: `/api/im/users/following/all`  
**方法**: GET  
**参数**: 无

### 评论API (Comments) 💬
**端点**: `/api/sns/v5/note/comment/list`  
**方法**: GET  
**参数**:
- `note_id` (必需): 笔记ID
- `start` (可选): 起始位置JSON对象，包含cursor和index
- `num` (可选): 数量，默认15
- `show_priority_sub_comments` (可选): 显示优先子评论
- `source` (可选): 来源
- `sort_strategy` (可选): 排序策略，默认"default"
- `page_context` (可选): 页面上下文，包含高亮词等
- `force_master` (可选): 强制主评论

### 笔记详情API (Note Details) 📝
**端点**: `/api/sns/v1/note/detailfeed/preload`  
**方法**: GET  
**参数**:
- `source` (必需): 来源
- `data` (必需): 包含笔记ID列表的JSON数据
- `ext` (可选): 扩展信息

### 用户信息API (User Info) 👤
**1. 当前用户信息**  
**端点**: `/api/sns/v3/user/me`  
**方法**: GET  
**参数**:
- `profile_page_head_exp` (可选): 个人主页头部实验
- `first_show` (可选): 首次显示
- `fill_info_guide_exp` (可选): 填写信息引导实验

**2. 用户权限**  
**端点**: `/api/sns/v1/user/me/authority`  
**方法**: GET  
**参数**:
- `scene` (可选): 场景

## 九、监控数据API域名分类

基于网络请求监控日志分析 (2025-08-05)，发现以下API域名和服务：

### 1. 数据收集和分析类 API

#### t2.xiaohongshu.com
- **端点**: `/api/collect`
- **方法**: POST
- **用途**: 通用数据收集，包括用户行为、事件追踪等
- **请求频率**: 最高频率调用的API（日志中出现100+次）
- **请求头**: 
  - `User-Agent`: Dalvik/2.1.0 (设备信息)
  - `request-from`: xhs-client

#### spider-tracker.xiaohongshu.com
- **端点**: `/api/spider`
- **方法**: POST
- **用途**: 爬虫检测和追踪系统
- **请求频率**: 中等（日志中出现16次）
- **请求头**:
  - `User-Agent`: Dalvik/2.1.0
  - `request-from`: xhs-client
  - `batch`: true (部分请求支持批量处理)

#### lng.xiaohongshu.com
- **端点**: `/api/collect`
- **方法**: POST
- **用途**: 位置/地理信息相关数据收集
- **请求频率**: 较低（日志中出现8次）
- **请求头**: 与t2.xiaohongshu.com相同

### 2. 性能监控类 API

#### apm-native.xiaohongshu.com
- **端点**: `/api/collect`
- **方法**: POST
- **用途**: 原生应用性能监控(APM - Application Performance Monitoring)
- **请求频率**: 中等（日志中出现10次）
- **特点**: 无特殊请求头

#### apm-fe.xiaohongshu.com
- **端点**: `/api/data`
- **方法**: POST
- **用途**: 前端性能监控
- **请求频率**: 中等（日志中出现8次）
- **请求头**:
  - `biz-type`: apm_fe
  - `batch`: true (支持批量上报)
  - `content-type`: text/plain (部分请求)

#### crash.xiaohongshu.com
- **端点**: `/api/v1/android/session`
- **方法**: POST
- **用途**: Android崩溃报告和会话管理
- **请求频率**: 低（日志中出现2次）
- **特点**: 无特殊请求头

### 3. 静态资源 CDN

#### fe-static.xhscdn.com
- **域名用途**: 前端静态资源CDN
- **资源类型**: React Native/Hermes字节码包(.zip格式)
- **请求方法**: GET
- **支持功能**: 
  - Range请求(断点续传)
  - 206 Partial Content响应

监控到的动态加载模块：
- `live-tools` (直播工具) - v1.0.0
- `live-wish` (直播心愿单) - v1.0.7
- `live-ranking` (直播排行榜) - v1.0.6
- `buyer-curation` (买手精选) - v1.4.0
- `live-org` (直播组织) - v1.0.9
- `life-private-message-rn` (私信) - v2.0.1
- `social-group` (社交群组) - v2.0.6
- `ads-creativity` (广告创意) - v1.1.2
- `life-pro-rn` (生活专业版) - v1.1.8
- `lancer-tok` (内容创作工具) - v0.7.0
- `gaia` - v1.1.14
- `wakanda` - v0.1.20
- `declare` (声明/公告) - v1.2.5
- `ares` - v1.0.3
- `ads-topic` (广告主题) - v0.11.7
- `cny-2025-main` (2025春节活动) - v2.2.10
- `venom` - v1.1.10

#### sns-avatar-qc.xhscdn.com
- **用途**: 用户头像CDN
- **支持功能**:
  - 图片格式转换(jpg/webp)
  - 尺寸调整(w=80/120/360等)
  - imageView2图片处理API
- **URL格式**: 
  - 头像: `/avatar/[用户ID]`
  - 横幅: `/user_banner/[用户ID]`

## 十、Shield签名深入分析

### Shield签名参数详解

#### shield（主签名）
- **格式**: Base64编码字符串
- **长度**: 固定134字符
- **解码后长度**: 100字节
- **特点**: 
  - 每个请求唯一，动态生成
  - 包含固定前缀（前20字节）
  - 后半部分包含变化数据（可能是时间戳+随机数+请求特征）
- **字节结构分析**:
  - 前20字节固定: `5d 80 00 40 00 30 00 00 00 10 00 00 05 30 00 00 05 33 51 61`
  - 可能的含义:
    - `5d 80` - 版本或类型标识
    - `00 40 00 30` - 长度或配置信息
    - 剩余部分 - 动态生成的签名数据
- **示例**: `XYAAQAAwAAAAEAAABTAAAAUzUWEe0xG1IbD9/c+qCLOlKGmTtFa+lG434HeOFeRKlFwoO2lrVnT53/reJXz8D5iMR+2PY0QgxIFWWKYLP32nhn0rKp3L4h0uLQ525VSZHT7oHO`

### Shield结构详解
基于大量样本分析，Shield签名具有以下精确结构：

**整体结构**：
- **总长度**: 134个字符（Base64编码）
- **解码后**: 100字节二进制数据
- **固定前缀**: 122个字符（91字节）
- **变化后缀**: 12个字符（9字节）

**固定前缀分析**：
```
XYAAQAAwAAAAEAAABTAAAAUzUWEe0xG1IbD9/c+qCLOlKGmTtFa+lG434HeOFeRKlFwoO2lrVnT53/reJXz8N5iMR+2PY0QgxIFWWKYLP32nhn0r
```
解码后的前32字节：
```
5d 80 00 40 00 30 00 00 00 10 00 00 05 30 00 00 
05 33 51 61 1e d3 11 b5 21 b0 fd fd cf aa 08 b3
```

**变化部分特征**：
- 长度固定为12个Base64字符
- 多以'I'或'K'开头
- 每次请求都不同，防止重放攻击
- 示例：`IZuj1vQq3EqM6vpubr8RqF`

### Shield生成机制
- **生成时机**: 在XhsHttpInterceptor拦截器中动态生成
- **依赖关系**: 
  - 6-8次System.currentTimeMillis()调用
  - 至少1次MD5计算（156字节输入）
- **Native实现**: 核心算法在libxhslonglink.so中（4.2MB，包含451个签名相关函数）
- **生成流程**:
  1. 收集请求信息（URL、Headers、时间戳等）
  2. 构建156字节的输入数据
  3. 进行MD5计算
  4. 调用Native方法生成100字节Shield数据
  5. Base64编码为134字符

## 十一、加密算法与签名机制

### 使用的加密算法
1. **MD5**: 
   - 用于生成各种哈希值
   - 固定输入长度：156字节（根据日志分析）
   - 每个请求会生成多个不同的MD5值
2. **SHA-1**: 用于签名验证
3. **HMAC**: 可能用于生成x-mini-sig
4. **Base64**: 用于编码shield等参数

### MD5输入结构（156字节）

Shield签名的生成基于156字节的固定格式输入：

```
位置      长度    内容            示例
0-3       4      HTTP方法         GET\0
4         1      分隔符           \0
5-60      56     URL路径          https://edith.xiaohongshu.com/api/sns/v2/...\0
61        1      分隔符           \0
62-74     13     时间戳           1754406179208\0
75        1      分隔符           \0
76-111    36     设备ID           77768303-7b28-3cd9-9d6b-64024836c7df\0
112       1      分隔符           \0
113-155   43     其他参数/填充     \0\0\0...
```

### 参数绑定关系

#### 不参与签名的参数：
- URL查询参数（?后面的内容）
- 请求体（POST数据）
- 大部分HTTP请求头

#### 参与签名的要素：
- HTTP方法（GET/POST等）
- URL路径（不含参数）
- 当前时间戳
- 设备唯一标识
- 请求序号/计数器

### 签名生成流程

```
请求构建
    ↓
构建156字节输入 → MD5计算 → Native处理 → 100字节Shield数据 → Base64编码 → 添加到Headers
                                    ↑
                              当前时间戳 + 随机数
```

## 十二、设备标识参数

### x-legacy-did
- **说明**: 设备唯一标识符
- **格式**: 标准UUID v4
- **长度**: 36字符
- **示例**: `77768303-7b28-3cd9-9d6b-64024836c7df`
- **生成方式**: 
  - UUID.randomUUID() 随机生成
  - 或基于设备信息的UUID.nameUUIDFromBytes()
- **存储**: SharedPreferences或内部文件

### x-legacy-fid
- **说明**: 设备指纹ID
- **长度**: 48字符（40字符SHA-1 + 8字符后缀）
- **示例**: `175438349610c7059c284eee5d4cb68cd2261c2d2ba6`
- **生成算法**:
  - SHA-1(设备信息拼接)
  - 设备信息包括: Build.MODEL + Build.MANUFACTURER + Android ID等
  - 后缀可能是版本号或校验码
- **特点**: 设备硬件变化会导致FID变化

### x-legacy-sid
- **说明**: 会话ID
- **格式**: `session.{timestamp}{random}`
- **示例**: `session.1754383566100708897767`

## 十三、核心类与方法

### XhsHttpInterceptor
**包名**: `com.xingin.shield.http.XhsHttpInterceptor`

**主要方法**:
- `intercept(Chain)` - 处理请求，添加签名
- `intercept(Chain, long)` - 带时间戳的拦截方法
- `initialize()` - 初始化
- `initializeNative()` - 初始化Native方法
- `ensureInitializedToken()` - 确保Token初始化

### Shield相关类
- `com.xingin.shield.http.ContextHolder` - 上下文管理
- `com.xingin.shield.http.ShieldLogger` - 日志记录
  - `buildSourceStart()` - 构建开始
  - `buildSourceEnd()` - 构建结束
- `com.xingin.shield.http.Base64Helper` - Base64编码辅助
  - `encodeToString()` - 编码为字符串
- `com.xingin.shield.http.c$a` - 混淆类
- `com.xingin.shield.http.c$b` - 混淆类

## 十四、请求拦截流程

1. **原始请求创建**
   - 应用层创建HTTP请求
   - 设置基础Headers（User-Agent等）

2. **拦截器处理流程**
   - `XhsHttpInterceptor.intercept(Chain)` 被调用
   - 调用 `ensureInitializedToken()` 确保初始化
   - 调用 `intercept(Chain, long)` 带时间戳参数处理
   - 密集调用时间戳（6-8次）用于签名生成
   - 计算MD5（固定156字节输入）
   - 生成shield签名（134字符）
   - 添加所有签名相关Headers：
     - shield（主签名）
     - xy-platform-info（平台信息）
     - X-XHS-Ext-Failover（故障转移）
     - X-XHS-Ext-DNSIsolateTag（DNS标签）
     - X-XHS-Ext-CustomIPList（IP列表）

3. **请求发送**
   - 带有完整签名的请求发送到服务器
   - 返回Response对象

## 十五、时间戳窗口和防重放机制

### 时间戳使用
- **调用频率**: 每个请求8-10次System.currentTimeMillis()调用
- **MD5输入**: 位置62-74包含13位时间戳
- **Shield**: 变化部分可能包含时间戳变形
- **x-mini-s1**: 包含加密的时间戳
- **x-mini-mua**: JWT标准的iat字段

### 时间窗口
- **推测有效期**: 5-10分钟
- **时钟偏差容忍**: ±30秒
- **过期响应**: HTTP 403 "Request expired"

### Shield防重放
- **一次性使用**: 每个Shield只能使用一次
- **服务器缓存**: 可能使用Redis缓存已用Shield
- **自动过期**: 超过时间窗口自动清除

### 验证流程
```
1. 时间戳检查 → 2. Shield唯一性检查 → 3. 签名验证 → 4. 业务处理
```

## 十六、Native层分析
根据逆向分析发现：
- **libxhslonglink.so**
  - 大小: 4236 KB
  - JNI函数: 63个
  - 签名相关函数: 451个
  - 包含大量sign、shield、encrypt、hash相关导出函数
- **libxhs_unwind.so**
  - 大小: 100 KB
  - 用于堆栈展开，可能与反调试有关

## 十七、技术要点总结

### 签名生成特点
- **动态性**: 每个请求的shield签名都不同
- **时效性**: 签名可能包含时间戳
- **设备绑定**: 签名与设备ID、指纹等绑定
- **Native实现**: 核心加密逻辑可能在Native层

### 反爬虫机制
- 多重设备指纹（did, fid, gid）
- 会话追踪（sid, traceid）
- 签名验证（shield, x-mini-sig）
- IP记录（X-XHS-Ext-CustomIPList）

### 安全特性
- JWT格式的x-mini-mua包含完整性校验
- 使用多种加密算法组合
- 请求参数与签名绑定
- Native层保护核心算法

### 注意事项
- Hook过程中App容易崩溃，说明有反调试保护
- 签名算法的具体实现在Native层，Java层只是调用接口
- 某些参数（如shield）的生成可能依赖服务器时间
- 需要注意进程选择，避免Hook到Google Play Services等系统进程

---

*文档更新时间: 2024-12-11*  
*Shield版本: 2.9.26*  
*App版本: 8.95.0*  
*监控日志版本: 8.94.0 (2025-08-05)*