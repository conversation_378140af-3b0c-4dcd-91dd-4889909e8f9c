#!/usr/bin/env python3
"""
测试TikHub API响应映射到本地API格式的功能
"""

import json
import asyncio
import sys
import os

# 添加当前目录到路径以导入xhsh模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入映射函数
from xhsh import map_tikhub_to_local_api

async def test_mapping():
    """测试TikHub到本地API的映射"""
    
    test_files = [
        ("/tmp/tikhub_图文_677839ac.json", "图文作品"),
        ("/tmp/tikhub_视频_6642193b.json", "视频作品"),
        ("/tmp/tikhub_动图_669b48eb.json", "动图作品")
    ]
    
    print("=" * 80)
    print("测试TikHub API响应映射到本地API格式")
    print("=" * 80)
    
    for file_path, desc in test_files:
        print(f"\n📋 测试 {desc}")
        print("-" * 40)
        
        try:
            # 读取TikHub响应
            with open(file_path, 'r', encoding='utf-8') as f:
                tikhub_response = json.load(f)
            
            # 执行映射
            local_api_response = await map_tikhub_to_local_api(tikhub_response)
            
            # 验证映射结果
            if local_api_response.get('code') == 0:
                print(f"✅ 映射成功!")
                data = local_api_response.get('data', {})
                
                # 打印关键信息
                print(f"   作品ID: {data.get('作品ID')}")
                print(f"   作品类型: {data.get('作品类型')}")
                print(f"   作品标题: {data.get('作品标题')[:30]}..." if data.get('作品标题') else "   作品标题: (无)")
                print(f"   作者昵称: {data.get('作者昵称')}")
                print(f"   下载地址数量: {len(data.get('下载地址', []))}")
                print(f"   动图地址数量: {len(data.get('动图地址', []))}")
                
                # 打印媒体URL
                download_urls = data.get('下载地址', [])
                gif_urls = data.get('动图地址', [])
                
                if download_urls:
                    print(f"   下载地址:")
                    for idx, url in enumerate(download_urls[:2], 1):  # 只显示前2个
                        print(f"     [{idx}] {url[:80]}...")
                
                if gif_urls:
                    print(f"   动图地址:")
                    for idx, url in enumerate(gif_urls[:2], 1):  # 只显示前2个
                        print(f"     [{idx}] {url[:80]}...")
                
                # 验证必要字段
                required_fields = ['作品ID', '作品类型', '作者昵称', '下载地址']
                missing_fields = [field for field in required_fields if not data.get(field)]
                
                if missing_fields:
                    print(f"   ⚠️ 缺少字段: {', '.join(missing_fields)}")
                else:
                    print(f"   ✅ 所有必要字段都存在")
                    
            else:
                print(f"❌ 映射失败: {local_api_response.get('msg')}")
                
        except FileNotFoundError:
            print(f"❌ 文件不存在: {file_path}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(test_mapping())