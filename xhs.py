#!/usr/bin/env python3
"""
小红书数据获取主程序 - 增量监控版本
包含正确的API路径和完整功能
支持数据库对比和无限循环监控

重要发现 - 小红书页面加载规律（2024年8月测试）：
=========================================

1. 初始加载规律（非常稳定）：
   - 初始总是显示10个作品（通常都是无效/已删除的）
   - 第一个ID稳定不变（如：689c0ef7000000001b01f4a3）
   - 无论直接访问还是从主页切换，结果一致
   - 等待时间（0-5秒）不影响初始数量

2. 滚动触发规律：
   - 任何轻微滚动（100px-500px）：触发API，页面变成20个作品
     = 10个无效（HTML显示）+ 10个有效（API返回）
   - 滚动到底部：页面重组，变成16个作品
     页面内容会被重新排序，第一个ID可能改变

3. 分层加载策略：
   - 第一层：10个缓存的作品ID（可能包含无效作品）
   - 第二层：滚动触发API，插入有效作品
   - 第三层：继续滚动，页面动态重组

4. 无效作品说明：
   - 这些是用户曾经点赞但后来被删除/设为私密的作品
   - 小红书保留了ID但API不返回内容
   - 在HTML中显示但无法获取详细信息

5. API行为：
   - API自动过滤无效作品，只返回有效内容
   - 每次API调用通常返回10条有效作品
   - API返回的顺序可能与页面显示顺序不同

6. 作者信息获取（重要）：
   - 从API返回的作品数据中，每个note都包含完整的user对象
   - user.user_id字段是24位十六进制字符串（如：5940df2a5e87e7630aa3ed8e）
   - 作者主页URL格式：https://www.xiaohongshu.com/user/profile/{user_id}
   - 可以直接通过构建URL访问作者主页，无需模拟点击
   - 作者主页会显示：昵称、粉丝数、获赞数、笔记数等信息
   
7. 重要API端点：
   - 点赞作品列表: /api/sns/web/v1/note/like/page
   - 收藏作品列表: /api/sns/web/v2/note/collect/page
   - 评论信息: /api/sns/web/v2/comment/page
   - 用户信息: /api/sns/web/v2/user/me（当前登录用户）
   - 注意：/api/sns/web/v1/user/otherinfo（其他用户信息）需要特定触发条件

8. 数据结构示例：
   API返回的note对象包含：
   {
     "note_id": "689d2219000000001d007eb9",
     "title": "作品标题",
     "user": {
       "user_id": "5940df2a5e87e7630aa3ed8e",
       "nickname": "好奇的森林",
       "avatar": "头像URL"
     },
     "xsec_token": "安全令牌（部分作品有）",
     "interact_info": {
       "liked_count": 点赞数,
       "collected_count": 收藏数,
       "comment_count": 评论数
     }
   }

注意事项：
- 页面显示的"第一个作品"取决于获取时机和滚动状态
- 真实的点赞顺序需要结合HTML显示和API数据综合判断
- 无效作品仍会占用页面位置，影响实际显示数量
- 作者ID是稳定的，可以用于构建主页链接和去重
"""

from playwright.sync_api import sync_playwright
import json
import re
from datetime import datetime
import time
import argparse
import random
import os
from dotenv import load_dotenv
from supabase import create_client, Client as SupabaseClient
import traceback

# 加载环境变量
load_dotenv()

# Supabase配置
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# 初始化Supabase客户端
supabase: SupabaseClient = None
if SUPABASE_URL and SUPABASE_KEY:
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase客户端初始化成功")
    except Exception as e:
        print(f"❌ Supabase客户端初始化失败: {e}")
        supabase = None
else:
    print("⚠️ 未配置Supabase环境变量")

class XHSMainFetcher:
    """
    小红书数据获取主程序 - 完整版
    获取账户的全部收藏和点赞作品信息（包括有效和无效的）
    
    正确的API路径：
    - 点赞作品: https://edith.xiaohongshu.com/api/sns/web/v1/note/like/page
    - 收藏作品: https://edith.xiaohongshu.com/api/sns/web/v2/note/collect/page
    """
    
    # API配置
    API_CONFIG = {
        'likes': {
            'url': 'https://edith.xiaohongshu.com/api/sns/web/v1/note/like/page',
            'domain': 'edith.xiaohongshu.com',
            'path': '/api/sns/web/v1/note/like/page',
            'tab': 'liked'
        },
        'favorites': {
            'url': 'https://edith.xiaohongshu.com/api/sns/web/v2/note/collect/page',
            'domain': 'edith.xiaohongshu.com',
            'path': '/api/sns/web/v2/note/collect/page',
            'tab': 'fav'
        },
        'author': {
            'url': 'https://edith.xiaohongshu.com/api/sns/web/v1/user/otherinfo',
            'domain': 'edith.xiaohongshu.com',
            'path': '/api/sns/web/v1/feed',  # 作者作品列表API
            'tab': 'note'  # 笔记标签页
        }
    }
    
    def __init__(self, cookie_string, user_id=None, user_name='unknown'):
        self.cookie_string = cookie_string
        self.user_id = user_id or "6712d0ee000000001d020d36"
        self.user_name = user_name  # 用于标识是500还是hulu
        self.all_notes = {}  # 所有笔记（包括有效和无效）
        self.api_notes = {}  # API返回的笔记
        self.html_only_notes = {}  # 仅在HTML中的笔记
        self.api_responses = []
        self.api_count = 0
        self.current_page = 0  # 当前页码
        self.notes_per_page = []  # 每页的笔记列表
        # 持久化浏览器配置目录（放在桌面）
        self.browser_profile_dir = f"/home/<USER>/Desktop/browser_data_{user_name}"
        
    def parse_cookies(self):
        """解析cookie字符串"""
        cookies = []
        for item in self.cookie_string.split('; '):
            if '=' in item:
                name, value = item.split('=', 1)
                cookies.append({
                    'name': name,
                    'value': value,
                    'domain': '.xiaohongshu.com',
                    'path': '/'
                })
        return cookies
    
    def check_cookie_validity(self):
        """检查cookie有效性 - 通过尝试获取API数据来验证"""
        print("\n🔍 检查Cookie有效性...")
        
        with sync_playwright() as p:
            context = self._get_browser_context(p)
            page = context.new_page()
            
            # 标记是否成功
            api_success = False
            
            # 监听API响应
            def handle_response(response):
                nonlocal api_success
                # 检查是否是API请求且返回成功
                if '/api/sns/web/v1' in response.url or '/api/sns/web/v2' in response.url:
                    if response.status == 200:
                        try:
                            data = response.json()
                            if data.get('code') == 0 or data.get('success') == True:
                                api_success = True
                                print(f"  ✅ API调用成功 - Cookie有效")
                        except:
                            pass
                    elif response.status == 401 or response.status == 403:
                        print(f"  ❌ API返回{response.status} - Cookie可能无效")
            
            page.on('response', handle_response)
            
            try:
                # 尝试访问点赞页面，触发API请求
                if self.user_id and self.user_id not in ["", "需要获取"]:
                    page.goto(f'https://www.xiaohongshu.com/user/profile/{self.user_id}?tab=liked')
                else:
                    page.goto('https://www.xiaohongshu.com/user/profile?tab=liked')
                
                # 等待一段时间让API请求完成
                page.wait_for_timeout(5000)
                
                # 如果API成功，则Cookie有效
                if api_success:
                    # 尝试获取user_id（如果需要）
                    if not self.user_id or self.user_id.strip() == "" or self.user_id == "需要获取":
                        current_url = page.url
                        import re
                        match = re.search(r'/user/profile/([a-f0-9]{24})', current_url)
                        if match:
                            self.user_id = match.group(1)
                            print(f"  📝 自动获取用户ID: {self.user_id}")
                    
                    context.close()
                    return True
                
                # 如果没有API成功但也没有明确的登录页面，给予警告但继续
                current_url = page.url.lower()
                if '/user/login' in current_url or 'login.xiaohongshu.com' in current_url:
                    print("  ❌ Cookie无效：跳转到登录页")
                    context.close()
                    return False
                elif 'captcha' in current_url or 'verification' in current_url:
                    print("  ⚠️ 需要安全验证，Cookie本身有效，继续执行")
                    context.close()
                    return True  # Cookie有效，只是需要验证
                else:
                    print("  ⚠️ 未检测到API调用，但未跳转登录页，尝试继续")
                    context.close()
                    return True
                        
            except Exception as e:
                print(f"  ⚠️ 检查过程出现异常: {e}")
                print("  ⚠️ 假设Cookie有效，继续执行")
                context.close()
                return True  # 出错时假设有效，让后续API调用来验证
    
    def check_notes_in_database(self, note_ids, data_type='likes'):
        """
        检查笔记是否已被当前用户标记过
        
        Args:
            note_ids: 笔记ID列表
            data_type: 'likes' 或 'favorites'
            
        Returns:
            dict: {note_id: 是否已被当前用户标记}
        """
        if not supabase or not note_ids:
            return {note_id: False for note_id in note_ids}
        
        try:
            # 构建查询字段名
            field_name = f"{self.user_name}_{data_type.replace('favorites', 'collect')}"
            
            # 批量查询数据库，只获取id和当前用户的标记字段
            response = supabase.table('xhs_notes').select(f'id, {field_name}').in_('id', note_ids).execute()
            
            # 检查每个笔记是否已被当前用户标记
            marked_ids = {item['id'] for item in response.data if item.get(field_name) == True}
            
            # 返回检查结果
            return {note_id: note_id in marked_ids for note_id in note_ids}
            
        except Exception as e:
            print(f"❌ 数据库查询失败: {e}")
            return {note_id: False for note_id in note_ids}
    
    def update_database(self, notes_data, data_type='likes'):
        """
        更新数据库中的笔记信息（智能合并，不覆盖其他用户的标记）
        
        Args:
            notes_data: 笔记数据字典 {note_id: note_info}
            data_type: 'likes' 或 'favorites'
        """
        if not supabase or not notes_data:
            return
        
        try:
            # 构建字段名
            field_name = f"{self.user_name}_{data_type.replace('favorites', 'collect')}"
            
            for note_id, note_info in notes_data.items():
                try:
                    # 首先检查记录是否已存在
                    existing = supabase.table('xhs_notes').select('*').eq('id', note_id).execute()
                    
                    if existing.data and len(existing.data) > 0:
                        # 记录已存在，更新可更新的字段
                        existing_record = existing.data[0]
                        
                        # 准备更新数据
                        update_data = {
                            'id': note_id,
                            field_name: True,  # 更新当前用户的标记
                            'last_update_time': datetime.now().isoformat()
                        }
                        
                        # 保留所有其他用户的标记
                        for key in ['500_likes', '500_collect', 'hulu_likes', 'hulu_collect']:
                            if key != field_name and key in existing_record:
                                update_data[key] = existing_record[key]
                        
                        # 始终更新基本信息（使用最新数据）
                        update_data['title'] = note_info.get('display_title', '') or note_info.get('title', '') or existing_record.get('title', '')
                        
                        # 始终更新作者信息（使用最新数据）
                        if 'user' in note_info:
                            user_data = note_info['user']
                            update_data['author_id'] = user_data.get('user_id', '') or existing_record.get('author_id', '')
                            update_data['author_name'] = user_data.get('nickname', '') or existing_record.get('author_name', '')
                            if user_data.get('user_id'):
                                update_data['author_link'] = f"https://www.xiaohongshu.com/user/profile/{user_data['user_id']}"
                            elif existing_record.get('author_link'):
                                update_data['author_link'] = existing_record['author_link']
                        else:
                            # 保留原有的作者信息
                            if existing_record.get('author_id'):
                                update_data['author_id'] = existing_record['author_id']
                            if existing_record.get('author_name'):
                                update_data['author_name'] = existing_record['author_name']
                            if existing_record.get('author_link'):
                                update_data['author_link'] = existing_record['author_link']
                        
                        # 始终更新交互数据（使用最新值）
                        if 'interact_info' in note_info:
                            interact = note_info['interact_info']
                            update_data['likes_count'] = interact.get('liked_count', '')
                            update_data['comments_count'] = interact.get('comment_count', '')
                            update_data['collect_count'] = interact.get('collected_count', '')
                            update_data['share_count'] = interact.get('share_count', '')
                        
                        # 更新笔记链接
                        update_data['note_link'] = f"https://www.xiaohongshu.com/explore/{note_id}"
                        
                        # 保留我们不会获取到的字段（如file_id等）
                        for key in ['file_id', 'short_link', 'note_type', 'topic', 'location', 'process_failed', 'timestamp']:
                            if key in existing_record and existing_record.get(key) is not None:
                                update_data[key] = existing_record[key]
                        
                    else:
                        # 新记录，创建完整数据
                        update_data = {
                            'id': note_id,
                            'title': note_info.get('display_title', '') or note_info.get('title', ''),
                            field_name: True,  # 标记该用户的点赞或收藏
                            'last_update_time': datetime.now().isoformat(),
                            # 初始化其他用户字段为 false
                            '500_likes': False,
                            '500_collect': False,
                            'hulu_likes': False,
                            'hulu_collect': False
                        }
                        
                        # 设置当前用户的字段为 True
                        update_data[field_name] = True
                        
                        # 添加作者信息
                        if 'user' in note_info:
                            user_data = note_info['user']
                            update_data['author_id'] = user_data.get('user_id', '')
                            update_data['author_name'] = user_data.get('nickname', '')
                            if user_data.get('user_id'):
                                update_data['author_link'] = f"https://www.xiaohongshu.com/user/profile/{user_data['user_id']}"
                        
                        # 添加交互数据
                        if 'interact_info' in note_info:
                            interact = note_info['interact_info']
                            update_data['likes_count'] = interact.get('liked_count', '')
                            update_data['comments_count'] = interact.get('comment_count', '')
                            update_data['collect_count'] = interact.get('collected_count', '')
                            update_data['share_count'] = interact.get('share_count', '')
                        
                        # 添加笔记链接
                        update_data['note_link'] = f"https://www.xiaohongshu.com/explore/{note_id}"
                    
                    # 更新数据库
                    supabase.table('xhs_notes').upsert(update_data).execute()
                    
                except Exception as e:
                    print(f"  ⚠️ 更新笔记 {note_id} 失败: {e}")
                    
            print(f"  ✅ 成功更新 {len(notes_data)} 条笔记到数据库")
            
        except Exception as e:
            print(f"❌ 数据库更新失败: {e}")
            traceback.print_exc()
    
    def _get_browser_context(self, playwright_instance):
        """获取持久化的浏览器上下文"""
        # 创建配置目录
        os.makedirs(self.browser_profile_dir, exist_ok=True)
        
        # 使用持久化上下文
        context = playwright_instance.chromium.launch_persistent_context(
            user_data_dir=self.browser_profile_dir,
            headless=True,  # 数据获取用无头模式
            args=['--no-sandbox'],
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0'
        )
        
        # 第一次启动时添加cookies（检查是否已登录）
        try:
            existing_cookies = context.cookies()
            has_xhs_cookies = any(cookie.get('domain', '').endswith('.xiaohongshu.com') for cookie in existing_cookies)
            if not has_xhs_cookies:
                context.add_cookies(self.parse_cookies())
        except:
            context.add_cookies(self.parse_cookies())
        
        return context
    
    def fetch_data_incremental(self, data_type='likes', max_pages=None):
        """
        增量获取数据，支持分页和数据库对比
        
        Args:
            data_type: 'likes' 获取点赞，'favorites' 获取收藏
            max_pages: 最大获取页数，None表示获取全部
            
        Returns:
            bool: 是否获取到新数据
        """
        if data_type not in self.API_CONFIG:
            raise ValueError(f"不支持的类型: {data_type}")
        
        config = self.API_CONFIG[data_type]
        self.all_notes.clear()
        self.api_notes.clear()
        self.html_only_notes.clear()
        self.api_responses.clear()
        self.api_count = 0
        self.current_page = 0
        self.notes_per_page = []
        
        print("="*60)
        type_name = {'likes': '点赞', 'favorites': '收藏'}.get(data_type, data_type)
        print(f"📊 增量获取 {self.user_name} 的{type_name}作品")
        print("="*60)
        print(f"用户ID: {self.user_id}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        has_new_data = False
        total_new_notes = 0
        
        with sync_playwright() as p:
            context = self._get_browser_context(p)
            page = context.new_page()
            
            has_more = True
            should_stop = False
            
            # 监听API响应
            def handle_response(response):
                nonlocal has_more, should_stop, has_new_data, total_new_notes
                url = response.url
                
                # 检查是否是目标API
                if config['path'] in url and config['domain'] in url:
                    self.api_count += 1
                    self.current_page = self.api_count
                    print(f"\n📄 第 {self.current_page} 页")
                    
                    try:
                        if response.status == 200:
                            data = response.json()
                            self.api_responses.append(data)
                            
                            # 提取数据
                            notes = data.get('data', {}).get('notes', [])
                            has_more = data.get('data', {}).get('has_more', False)
                            
                            print(f"  获取到: {len(notes)} 条作品")
                            
                            if notes:
                                # 保存本页笔记
                                page_notes = {}
                                page_note_ids = []
                                
                                for note in notes:
                                    note_id = note.get('note_id')
                                    if note_id:
                                        self.api_notes[note_id] = note
                                        page_notes[note_id] = note
                                        page_note_ids.append(note_id)
                                        self.all_notes[note_id] = {
                                            'note_id': note_id,
                                            'source': 'api',
                                            'has_token': bool(note.get('xsec_token')),
                                            'api_data': note,
                                            'html_data': None
                                        }
                                
                                self.notes_per_page.append(page_note_ids)
                                
                                # 检查数据库
                                db_check = self.check_notes_in_database(page_note_ids, data_type)
                                new_notes = {nid: note for nid, note in page_notes.items() if not db_check.get(nid, False)}
                                existing_notes = {nid: note for nid, note in page_notes.items() if db_check.get(nid, False)}
                                existing_count = len(existing_notes)
                                
                                print(f"  数据库中已存在: {existing_count} 条")
                                print(f"  新作品: {len(new_notes)} 条")
                                
                                # 无论是否是新数据，都更新到数据库（更新最新的互动数据等）
                                if page_notes:
                                    if new_notes:
                                        has_new_data = True
                                        total_new_notes += len(new_notes)
                                    # 更新所有获取到的数据（包括已存在的）
                                    self.update_database(page_notes, data_type)
                                
                                # 如果本页全部都已被当前用户标记过，停止获取
                                if existing_count == len(page_note_ids) and len(page_note_ids) > 0:
                                    print(f"\n⏹️ 本页所有作品都已被{self.user_name}标记过，停止获取")
                                    should_stop = True
                                    has_more = False
                                
                                # 达到最大页数限制
                                if max_pages and self.current_page >= max_pages:
                                    print(f"\n⏹️ 已达到最大页数限制 ({max_pages} 页)")
                                    should_stop = True
                                    has_more = False
                        else:
                            print(f"  API返回错误: {response.status}")
                    except Exception as e:
                        print(f"  解析失败: {e}")
            
            page.on('response', handle_response)
            
            # 访问用户页面
            page.goto(f'https://www.xiaohongshu.com/user/profile/{self.user_id}?tab={config["tab"]}')
            page.wait_for_timeout(3000)
            
            # 循环滚动获取数据
            scroll_count = 0
            while has_more and not should_stop:
                scroll_count += 1
                
                # 滚动页面
                page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
                page.wait_for_timeout(random.randint(2000, 3000))
                
                # 防止无限滚动
                if scroll_count > 100:
                    print("\n⚠️ 滚动次数过多，停止获取")
                    break
            
            context.close()
        
        # 统计
        print(f"\n{'='*60}")
        print(f"📊 {self.user_name} {type_name}作品获取完成:")
        print(f"  总页数: {self.current_page}")
        print(f"  总作品数: {len(self.all_notes)}")
        print(f"  新增作品: {total_new_notes}")
        print(f"  API调用次数: {self.api_count}")
        print(f"{'='*60}")
        
        return has_new_data
    
    def get_subscribed_authors(self):
        """
        从数据库获取所有订阅的作者（sub=true）
        
        Returns:
            list: 订阅作者列表 [{userid, nickname, ...}]
        """
        if not supabase:
            print("⚠️ 数据库未连接，无法获取订阅作者")
            return []
        
        try:
            # 查询所有sub=true的作者
            response = supabase.table('xhs_user').select('userid, nickname').eq('sub', True).execute()
            
            if response.data:
                print(f"👥 找到 {len(response.data)} 个订阅作者")
                return response.data
            else:
                print("ℹ️ 没有找到订阅作者")
                return []
                
        except Exception as e:
            print(f"❌ 获取订阅作者失败: {e}")
            return []
    
    def fetch_author_works(self, author_id, author_name, max_pages=None):
        """
        获取作者的所有作品（增量获取）
        
        Args:
            author_id: 作者ID
            author_name: 作者昵称
            max_pages: 最大获取页数
            
        Returns:
            bool: 是否获取到新数据
        """
        config = self.API_CONFIG['author']
        self.all_notes.clear()
        self.api_notes.clear()
        self.api_responses.clear()
        self.api_count = 0
        self.current_page = 0
        
        print("="*60)
        print(f"🌟 获取作者 {author_name} 的作品")
        print(f"作者ID: {author_id}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        has_new_data = False
        total_new_notes = 0
        
        with sync_playwright() as p:
            context = self._get_browser_context(p)
            page = context.new_page()
            
            has_more = True
            should_stop = False
            
            # 监听API响应
            def handle_response(response):
                nonlocal has_more, should_stop, has_new_data, total_new_notes
                url = response.url
                
                # 检查是否是作品列表API
                if '/api/sns/web/v1/feed' in url or '/user/posted' in url:
                    self.api_count += 1
                    self.current_page = self.api_count
                    print(f"\n📄 第 {self.current_page} 页")
                    
                    try:
                        if response.status == 200:
                            data = response.json()
                            self.api_responses.append(data)
                            
                            # 提取数据
                            notes = data.get('data', {}).get('notes', [])
                            has_more = data.get('data', {}).get('has_more', False)
                            
                            print(f"  获取到: {len(notes)} 条作品")
                            
                            if notes:
                                # 保存本页笔记
                                page_notes = {}
                                page_note_ids = []
                                
                                for note in notes:
                                    note_id = note.get('note_id')
                                    if note_id:
                                        self.api_notes[note_id] = note
                                        page_notes[note_id] = note
                                        page_note_ids.append(note_id)
                                
                                # 检查数据库（只检查是否存在）
                                db_check = self.check_notes_exist_in_database(page_note_ids)
                                new_notes = {nid: note for nid, note in page_notes.items() if not db_check.get(nid, False)}
                                existing_notes = {nid: note for nid, note in page_notes.items() if db_check.get(nid, False)}
                                existing_count = len(existing_notes)
                                
                                print(f"  数据库中已存在: {existing_count} 条")
                                print(f"  新作品: {len(new_notes)} 条")
                                
                                # 无论是否是新数据，都更新到数据库（更新最新的互动数据等）
                                if page_notes:
                                    if new_notes:
                                        has_new_data = True
                                        total_new_notes += len(new_notes)
                                    # 更新所有获取到的数据（包括已存在的）
                                    self.update_author_works_to_database(page_notes, author_id, author_name)
                                
                                # 如果本页全部都已存在，停止获取
                                if existing_count == len(page_note_ids) and len(page_note_ids) > 0:
                                    print(f"\n⏹️ 本页所有作品都已在数据库中，停止获取")
                                    should_stop = True
                                    has_more = False
                                
                                # 达到最大页数限制
                                if max_pages and self.current_page >= max_pages:
                                    print(f"\n⏹️ 已达到最大页数限制 ({max_pages} 页)")
                                    should_stop = True
                                    has_more = False
                        else:
                            print(f"  API返回错误: {response.status}")
                    except Exception as e:
                        print(f"  解析失败: {e}")
            
            page.on('response', handle_response)
            
            # 访问作者主页
            author_url = f'https://www.xiaohongshu.com/user/profile/{author_id}?tab=note'
            print(f"访问作者主页: {author_url}")
            page.goto(author_url)
            page.wait_for_timeout(3000)
            
            # 循环滚动获取数据
            scroll_count = 0
            while has_more and not should_stop:
                scroll_count += 1
                
                # 滚动页面
                page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
                page.wait_for_timeout(random.randint(2000, 3000))
                
                # 防止无限滚动
                if scroll_count > 100:
                    print("\n⚠️ 滚动次数过多，停止获取")
                    break
            
            context.close()
        
        # 统计
        print(f"\n{'='*60}")
        print(f"📊 作者 {author_name} 作品获取完成:")
        print(f"  总页数: {self.current_page}")
        print(f"  总作品数: {len(self.api_notes)}")
        print(f"  新增作品: {total_new_notes}")
        print(f"{'='*60}")
        
        return has_new_data
    
    def check_notes_exist_in_database(self, note_ids):
        """
        检查笔记是否已存在于数据库中（不管哪个用户标记过）
        
        Args:
            note_ids: 笔记ID列表
            
        Returns:
            dict: {note_id: exists_in_db}
        """
        if not supabase or not note_ids:
            return {note_id: False for note_id in note_ids}
        
        try:
            # 批量查询数据库
            response = supabase.table('xhs_notes').select('id').in_('id', note_ids).execute()
            
            existing_ids = {item['id'] for item in response.data if item.get('id')}
            
            # 返回检查结果
            return {note_id: note_id in existing_ids for note_id in note_ids}
            
        except Exception as e:
            print(f"❌ 数据库查询失败: {e}")
            return {note_id: False for note_id in note_ids}
    
    def update_author_works_to_database(self, notes_data, author_id, author_name):
        """
        更新作者作品到数据库（智能合并，不覆盖重要字段）
        
        Args:
            notes_data: 笔记数据字典 {note_id: note_info}
            author_id: 作者ID
            author_name: 作者昵称
        """
        if not supabase or not notes_data:
            return
        
        try:
            for note_id, note_info in notes_data.items():
                try:
                    # 首先检查记录是否已存在
                    existing = supabase.table('xhs_notes').select('*').eq('id', note_id).execute()
                    
                    if existing.data and len(existing.data) > 0:
                        # 记录已存在，更新可更新的字段
                        existing_record = existing.data[0]
                        
                        update_data = {
                            'id': note_id,
                            'title': note_info.get('display_title', '') or note_info.get('title', '') or existing_record.get('title', ''),
                            'author_id': author_id,
                            'author_name': author_name,
                            'author_link': f"https://www.xiaohongshu.com/user/profile/{author_id}",
                            'note_link': f"https://www.xiaohongshu.com/explore/{note_id}",
                            'last_update_time': datetime.now().isoformat()
                        }
                        
                        # 保留所有用户标记字段
                        for key in ['500_likes', '500_collect', 'hulu_likes', 'hulu_collect']:
                            if key in existing_record:
                                update_data[key] = existing_record[key]
                        
                        # 更新交互数据（使用最新值）
                        if 'interact_info' in note_info:
                            interact = note_info['interact_info']
                            update_data['likes_count'] = interact.get('liked_count', '')
                            update_data['comments_count'] = interact.get('comment_count', '')
                            update_data['collect_count'] = interact.get('collected_count', '')
                            update_data['share_count'] = interact.get('share_count', '')
                        
                        # 更新发布时间
                        if 'time' in note_info:
                            update_data['publish_time'] = note_info.get('time', '')
                        elif existing_record.get('publish_time'):
                            update_data['publish_time'] = existing_record['publish_time']
                        
                        # 保留我们不会获取到的字段
                        for key in ['file_id', 'short_link', 'note_type', 'topic', 'location', 'process_failed', 'timestamp', 'content']:
                            if key in existing_record and existing_record.get(key) is not None:
                                update_data[key] = existing_record[key]
                        
                    else:
                        # 新记录，创建完整数据
                        update_data = {
                            'id': note_id,
                            'title': note_info.get('display_title', '') or note_info.get('title', ''),
                            'author_id': author_id,
                            'author_name': author_name,
                            'author_link': f"https://www.xiaohongshu.com/user/profile/{author_id}",
                            'note_link': f"https://www.xiaohongshu.com/explore/{note_id}",
                            'last_update_time': datetime.now().isoformat(),
                            # 初始化用户标记字段
                            '500_likes': False,
                            '500_collect': False,
                            'hulu_likes': False,
                            'hulu_collect': False
                        }
                        
                        # 添加交互数据
                        if 'interact_info' in note_info:
                            interact = note_info['interact_info']
                            update_data['likes_count'] = interact.get('liked_count', '')
                            update_data['comments_count'] = interact.get('comment_count', '')
                            update_data['collect_count'] = interact.get('collected_count', '')
                            update_data['share_count'] = interact.get('share_count', '')
                        
                        # 添加发布时间
                        if 'time' in note_info:
                            update_data['publish_time'] = note_info.get('time', '')
                    
                    # 更新数据库
                    supabase.table('xhs_notes').upsert(update_data).execute()
                    
                except Exception as e:
                    print(f"  ⚠️ 更新作品 {note_id} 失败: {e}")
                    
            print(f"  ✅ 成功更新 {len(notes_data)} 条作者作品到数据库")
            
        except Exception as e:
            print(f"❌ 数据库更新失败: {e}")
            traceback.print_exc()
    
    def fetch_data(self, data_type='likes'):
        """
        保留原有的fetch_data方法用于兼容性
        """
        if data_type not in self.API_CONFIG:
            raise ValueError(f"不支持的类型: {data_type}")
        
        # 调用增量获取方法
        result = self.fetch_data_incremental(data_type, max_pages=None)
        
        # 返回兼容的数据结构
        return {
            'type': data_type,
            'total_count': len(self.all_notes),
            'api_count': len(self.api_notes),
            'html_only_count': len(self.html_only_notes),
            'all_notes': dict(self.all_notes),
            'api_notes': dict(self.api_notes),
            'html_only_notes': dict(self.html_only_notes),
            'api_responses': list(self.api_responses)
        }

def create_persistent_browsers(USERS):
    """
    创建两个持久的浏览器实例
    浏览器会一直保持运行，程序退出后不会关闭
    
    Returns:
        dict: 包含两个浏览器context的字典
    """
    from playwright.sync_api import sync_playwright
    import os
    
    browsers = {}
    playwright = sync_playwright().start()
    
    # 检测是否有显示器（检查DISPLAY环境变量）
    has_display = os.environ.get('DISPLAY') is not None
    
    for user_name, account in USERS.items():
        print(f"\n🌟 创建 {user_name} 的持久浏览器...")
        
        # 创建配置目录（放在桌面）
        profile_dir = f"/home/<USER>/Desktop/browser_data_{user_name}"
        os.makedirs(profile_dir, exist_ok=True)
        
        # 解析cookies
        cookies = []
        for item in account['cookie'].split('; '):
            if '=' in item:
                name, value = item.split('=', 1)
                cookies.append({
                    'name': name,
                    'value': value,
                    'domain': '.xiaohongshu.com',
                    'path': '/'
                })
        
        # 根据环境决定是否显示浏览器窗口
        if has_display:
            print(f"  📺 检测到显示器，将显示浏览器窗口")
            browser_args = [
                '--no-sandbox',
                '--start-maximized',
                f'--window-position={0 if user_name == "500" else 960},0',  # 500在左，hulu在右
                '--window-size=960,1080'
            ]
            headless = False
        else:
            print(f"  🖥️ 服务器环境，使用无头模式")
            browser_args = ['--no-sandbox']
            headless = True
        
        # 创建持久化上下文
        context = playwright.chromium.launch_persistent_context(
            user_data_dir=profile_dir,
            headless=headless,
            args=browser_args,
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0'
        )
        
        # 添加cookies（如果还没有）
        try:
            existing_cookies = context.cookies()
            has_xhs_cookies = any(cookie.get('domain', '').endswith('.xiaohongshu.com') for cookie in existing_cookies)
            if not has_xhs_cookies:
                context.add_cookies(cookies)
                print(f"  ✅ 已添加 {user_name} 的cookies")
        except:
            context.add_cookies(cookies)
        
        # 打开主页
        page = context.new_page()
        page.goto(f"https://www.xiaohongshu.com/user/profile/{account['user_id']}")
        
        browsers[user_name] = {
            'context': context,
            'page': page,
            'playwright': playwright
        }
        
        print(f"  ✅ {user_name} 浏览器已创建并打开主页")
    
    return browsers

def monitor_mode():
    """
    监控模式：无限循环获取两个账号的点赞和收藏
    顺序：500点赞 -> 500收藏 -> hulu点赞 -> hulu收藏 -> 循环
    """
    # 写死两个用户的信息
    USERS = {
        '500': {
            'cookie': """sec_poison_id=aa23e4e4-6992-40c2-8685-************; websectiga=59d3ef1e60c4aa37a7df3c23467bd46d7f1da0b1918cf335ee7f2e9e52ac04cf; acw_tc=0a4acc4417550896222047301eaa8a2c7e4d13794c48dcb4bb447c5df1716a; unread={%22ub%22:%22689c81fe000000001d028de8%22%2C%22ue%22:%22689c52c0000000001d009784%22%2C%22uc%22:46}; web_session=040069b66693a6245d6037caa93a4bff95a723; gid=yjyiYjK2YDhDyjyWdK20dxVfJDf3lqkyKjYxj7Mq0CYT7Vq8FEdiA7888y84J448q0fSS4fD; loadts=*************; webBuild=4.75.3; xsecappid=xhs-pc-web; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517533553043826274456potvgomhglugj4nk; galaxy.creator.beaker.session.id=1754042004131095150170; galaxy_creator_session_id=RAie0siYy4isSIm5TWgg0yvTZ6FDFNdSalRZ; x-user-id-creator.xiaohongshu.com=6712d0ee000000001d020d36; customerClientId=488105734416090; abRequestId=c32b37081d3bffd0e1153c2f691bcb79; a1=1950d6564ffgk3m2dbci8v05vfthugxuzajc8qajp30000383612; webId=c32b37081d3bffd0e1153c2f691bcb79""",
            'user_id': '6712d0ee000000001d020d36',
            'nickname': '500',
            'output_file': '500.json'
        },
        'hulu': {
            'cookie': """xsecappid=xhs-pc-web; a1=1943e456fe40owiyww8lx095lc2up8kaomnh2des630000346924; webId=e36801e48894c0110568389dce131745; gid=yj4qd4K8yDlqyj4qd42Ki6fdd48lE3vEEY1CIMK8j21SJxq87UYT0J888q4KjJ48JjJd4Wjd; abRequestId=e36801e48894c0110568389dce131745; web_session=040069b1c5d53457ad113083a93a4ba589c0e2; loadts=1755106193960; x-user-id-creator.xiaohongshu.com=60b1967c000000000101fd47; customerClientId=848303152042021; websectiga=7750c37de43b7be9de8ed9ff8ea0e576519e8cd2157322eb972ecb429a7735d4; webBuild=4.75.3; acw_tc=0a4aaa8d17551050411181007e219e25d8273bc7e644e03fe4b19b4020cc99; sec_poison_id=2ac35908-9b8f-4f45-ba33-dad62a25b385; unread={%22ub%22:%22689765e7000000002301b229%22%2C%22ue%22:%226899bbb800000000050074e6%22%2C%22uc%22:38}""",
            'user_id': '60b1967c000000000101fd47',
            'nickname': 'hulu',
            'output_file': 'hulu.json'
        }
    }
    
    print("🚀 小红书监控模式启动")
    print("="*60)
    print("监控账号: 500, hulu")
    print("监控内容: 点赞和收藏作品")
    print("监控策略: 增量获取，当页面所有作品都已存在时停止")
    print("="*60)
    
    # 暂时注释掉持久浏览器创建，先让程序正常运行
    # print("\n📌 创建持久浏览器实例...")
    # persistent_browsers = create_persistent_browsers(USERS)
    # print("\n✅ 两个浏览器窗口已创建，您可以随时查看和操作它们")
    # print("提示：浏览器窗口会保持开启，即使程序退出")
    # print("="*60)
    persistent_browsers = {}
    
    cycle_count = 0
    
    try:
        while True:
            cycle_count += 1
            print(f"\n{'='*80}")
            print(f"🔄 第 {cycle_count} 轮监控开始")
            print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*80}")
            
            # 任务列表：500点赞 -> 500收藏 -> hulu点赞 -> hulu收藏
            tasks = [
                ('500', 'likes'),
                ('500', 'favorites'),
                ('hulu', 'likes'),
                ('hulu', 'favorites')
            ]
            
            total_new_in_cycle = 0
            
            for user_name, data_type in tasks:
                account = USERS[user_name]
                type_name = {'likes': '点赞', 'favorites': '收藏'}.get(data_type, data_type)
                
                print(f"\n{'='*60}")
                print(f"📍 处理任务: {user_name} - {type_name}")
                print(f"{'='*60}")
                
                try:
                    # 创建fetcher实例
                    fetcher = XHSMainFetcher(account['cookie'], account['user_id'], user_name)
                
                    # 检查cookie有效性
                    if not fetcher.check_cookie_validity():
                        print(f"❌ 账号 {user_name} 的Cookie无效，跳过此任务")
                        continue
                    
                    # 增量获取数据
                    has_new_data = fetcher.fetch_data_incremental(data_type)
                    
                    if has_new_data:
                        total_new_in_cycle += len([n for n in fetcher.api_notes.values()])
                        print(f"✅ {user_name} {type_name}任务完成，获取到新数据")
                    else:
                        print(f"ℹ️ {user_name} {type_name}任务完成，无新数据")
                    
                    # 任务间休息3-5秒
                    wait_time = random.randint(3, 5)
                    print(f"⏳ 等待 {wait_time} 秒后继续下一个任务...")
                    time.sleep(wait_time)
                    
                except Exception as e:
                    print(f"❌ 处理 {user_name} {type_name} 时出错: {e}")
                    traceback.print_exc()
                    continue
        
            # 处理订阅作者的作品
            print(f"\n{'='*60}")
            print(f"🌟 处理订阅作者作品")
            print(f"{'='*60}")
            
            # 使用500账号的cookie来获取作者作品
            fetcher_for_authors = XHSMainFetcher(USERS['500']['cookie'], USERS['500']['user_id'], '500')
            
            # 获取订阅作者列表
            subscribed_authors = fetcher_for_authors.get_subscribed_authors()
        
            if subscribed_authors:
                print(f"开始处理 {len(subscribed_authors)} 个订阅作者...")
                
                for author in subscribed_authors:
                    author_id = author.get('userid')
                    author_name = author.get('nickname', '未知')
                    
                    print(f"\n👤 处理作者: {author_name} (ID: {author_id})")
                    
                    try:
                        # 获取作者作品
                        has_new_works = fetcher_for_authors.fetch_author_works(author_id, author_name)
                    
                        if has_new_works:
                            total_new_in_cycle += len([n for n in fetcher_for_authors.api_notes.values()])
                            print(f"✅ 作者 {author_name} 的作品获取完成，有新作品")
                        else:
                            print(f"ℹ️ 作者 {author_name} 的作品获取完成，无新作品")
                        
                        # 作者间休息2-4秒
                        wait_time = random.randint(2, 4)
                        print(f"⏳ 等待 {wait_time} 秒后处理下一个作者...")
                        time.sleep(wait_time)
                        
                    except Exception as e:
                        print(f"❌ 处理作者 {author_name} 时出错: {e}")
                        continue
            else:
                print("ℹ️ 没有订阅作者需要处理")
        
            # 本轮统计
            print(f"\n{'='*80}")
            print(f"📊 第 {cycle_count} 轮监控完成")
            print(f"本轮新增作品总数: {total_new_in_cycle}")
            print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*80}")
            
            # 循环间隔（30-60秒）
            wait_minutes = random.randint(30, 60)
            print(f"\n💤 休息 {wait_minutes} 秒后开始下一轮监控...")
            print(f"下次监控时间: {(datetime.now() + timedelta(seconds=wait_minutes)).strftime('%Y-%m-%d %H:%M:%S')}")
        
            time.sleep(wait_minutes)
    
    except KeyboardInterrupt:
        print("\n\n🛑 收到退出信号...")
        print("💡 提示：浏览器窗口将保持开启，您可以继续查看和操作")
    except Exception as e:
        print(f"\n❌ 监控模式异常: {e}")
        traceback.print_exc()
    finally:
        print("\n✅ 程序退出")
        print("💡 浏览器配置已保存，下次启动将自动加载")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='小红书数据获取工具')
    parser.add_argument('--mode', choices=['single', 'monitor'], 
                      default='monitor', help='运行模式：single-单次获取，monitor-监控模式')
    parser.add_argument('--user', choices=['500', 'hulu', 'both'], 
                      default='both', help='获取哪个用户的数据（仅single模式）')
    
    args = parser.parse_args()
    
    if args.mode == 'monitor':
        # 监控模式
        monitor_mode()
    else:
        # 单次获取模式（保留原有功能）
        # 写死两个用户的信息
        USERS = {
            '500': {
                'cookie': """sec_poison_id=aa23e4e4-6992-40c2-8685-************; websectiga=59d3ef1e60c4aa37a7df3c23467bd46d7f1da0b1918cf335ee7f2e9e52ac04cf; acw_tc=0a4acc4417550896222047301eaa8a2c7e4d13794c48dcb4bb447c5df1716a; unread={%22ub%22:%22689c81fe000000001d028de8%22%2C%22ue%22:%22689c52c0000000001d009784%22%2C%22uc%22:46}; web_session=040069b66693a6245d6037caa93a4bff95a723; gid=yjyiYjK2YDhDyjyWdK20dxVfJDf3lqkyKjYxj7Mq0CYT7Vq8FEdiA7888y84J448q0fSS4fD; loadts=*************; webBuild=4.75.3; xsecappid=xhs-pc-web; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517533553043826274456potvgomhglugj4nk; galaxy.creator.beaker.session.id=1754042004131095150170; galaxy_creator_session_id=RAie0siYy4isSIm5TWgg0yvTZ6FDFNdSalRZ; x-user-id-creator.xiaohongshu.com=6712d0ee000000001d020d36; customerClientId=488105734416090; abRequestId=c32b37081d3bffd0e1153c2f691bcb79; a1=1950d6564ffgk3m2dbci8v05vfthugxuzajc8qajp30000383612; webId=c32b37081d3bffd0e1153c2f691bcb79""",
                'user_id': '6712d0ee000000001d020d36',
                'nickname': '500',
                'output_file': '500.json'
            },
            'hulu': {
                'cookie': """xsecappid=xhs-pc-web; a1=1943e456fe40owiyww8lx095lc2up8kaomnh2des630000346924; webId=e36801e48894c0110568389dce131745; gid=yj4qd4K8yDlqyj4qd42Ki6fdd48lE3vEEY1CIMK8j21SJxq87UYT0J888q4KjJ48JjJd4Wjd; abRequestId=e36801e48894c0110568389dce131745; web_session=040069b1c5d53457ad113083a93a4ba589c0e2; loadts=1755106193960; x-user-id-creator.xiaohongshu.com=60b1967c000000000101fd47; customerClientId=848303152042021; websectiga=7750c37de43b7be9de8ed9ff8ea0e576519e8cd2157322eb972ecb429a7735d4; webBuild=4.75.3; acw_tc=0a4aaa8d17551050411181007e219e25d8273bc7e644e03fe4b19b4020cc99; sec_poison_id=2ac35908-9b8f-4f45-ba33-dad62a25b385; unread={%22ub%22:%22689765e7000000002301b229%22%2C%22ue%22:%226899bbb800000000050074e6%22%2C%22uc%22:38}""",
                'user_id': '60b1967c000000000101fd47',
                'nickname': 'hulu',
                'output_file': 'hulu.json'
            }
        }
        
        print("🚀 小红书数据获取工具 v2.0")
        print("="*60)
        
        # 确定要处理的账号
        accounts_to_process = []
        
        if args.user == '500':
            accounts_to_process.append(USERS['500'])
        elif args.user == 'hulu':
            accounts_to_process.append(USERS['hulu'])
        else:  # both
            accounts_to_process.append(USERS['500'])
            accounts_to_process.append(USERS['hulu'])
        
        # 处理每个账号
        for account in accounts_to_process:
            print(f"\n{'='*60}")
            print(f"处理账号: {account['nickname']}")
            print(f"{'='*60}")
            
            fetcher = XHSMainFetcher(account['cookie'], account['user_id'], account['nickname'])
            
            # 检查cookie有效性
            if not fetcher.check_cookie_validity():
                print(f"❌ 账号 {account['nickname']} 的Cookie无效，跳过此账号")
                continue
            
            # 获取点赞作品
            print(f"\n📍 获取{account['nickname']}的点赞作品...")
            likes_data = fetcher.fetch_data('likes')
            print(f"✅ 点赞作品获取完成: {likes_data['total_count']} 条")
            
            # 获取收藏作品
            print(f"\n📍 获取{account['nickname']}的收藏作品...")
            favorites_data = fetcher.fetch_data('favorites')
            print(f"✅ 收藏作品获取完成: {favorites_data['total_count']} 条")
            
            print(f"\n✅ 账号 {account['nickname']} 处理完成")

if __name__ == "__main__":
    # 添加缺失的导入
    from datetime import timedelta
    main()