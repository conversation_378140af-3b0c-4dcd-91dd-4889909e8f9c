{"name": "xhs-scraper", "version": "2.0.0", "description": "小红书数据获取工具 - JavaScript版本", "main": "xhs.js", "type": "module", "scripts": {"start": "node xhs.js", "monitor": "node xhs.js --mode monitor", "single": "node xhs.js --mode single"}, "dependencies": {"playwright": "^1.40.0", "@supabase/supabase-js": "^2.49.8", "dotenv": "^16.5.0", "commander": "^11.1.0"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "scraper", "playwright", "supabase"], "author": "", "license": "MIT"}